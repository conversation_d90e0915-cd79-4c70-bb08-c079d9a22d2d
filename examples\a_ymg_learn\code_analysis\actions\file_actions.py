#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2024/3/11
<AUTHOR> ale<PERSON><PERSON><PERSON>
@File    : file_actions.py
"""
from pathlib import Path
from datetime import datetime
from typing import Dict, List

from metagpt.actions import Action
from metagpt.logs import logger

from ..schema import FileInfo

class ParseFileAction(Action):
    """解析文件路径"""
    name: str = "ParseFileAction"
    
    async def run(self, target_path: str) -> Dict:
        """分析目标路径，获取文件列表"""
        try:
            path = Path(target_path)
            if not path.exists():
                logger.error(f"路径 {target_path} 不存在")
                return {
                    'error': f'路径 {target_path} 不存在',
                    'analyzed_at': datetime.now().isoformat()
                }
            
            files = []
            
            # 如果是文件
            if path.is_file():
                if not str(path).endswith('.py'):
                    logger.warning(f"忽略非Python文件: {path}")
                    return {
                        'error': f'指定的文件不是Python文件: {path}',
                        'analyzed_at': datetime.now().isoformat()
                    }
                
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    files.append(FileInfo(
                        file_path=str(path),
                        content=content,
                        size=path.stat().st_size,
                        type='python',
                        last_modified=datetime.fromtimestamp(path.stat().st_mtime).isoformat()
                    ).dict())
                except Exception as e:
                    logger.error(f"读取文件 {path} 出错: {str(e)}")
                    return {
                        'error': f'读取文件出错: {str(e)}',
                        'analyzed_at': datetime.now().isoformat()
                    }
            
            # 如果是目录
            elif path.is_dir():
                for py_file in path.glob('**/*.py'):
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        files.append(FileInfo(
                            file_path=str(py_file),
                            content=content,
                            size=py_file.stat().st_size,
                            type='python',
                            last_modified=datetime.fromtimestamp(py_file.stat().st_mtime).isoformat()
                        ).dict())
                    except Exception as e:
                        logger.error(f"读取文件 {py_file} 出错: {str(e)}")
            
            return {
                'files': files,
                'total_files': len(files),
                'target_path': str(path),
                'analyzed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            error_message = f'分析路径时出错: {str(e)}'
            logger.error(error_message)
            return {
                'error': error_message,
                'analyzed_at': datetime.now().isoformat()
            } 