#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2024/3/11
<AUTHOR> ale<PERSON><PERSON><PERSON>
@File    : function_analyzer.py
"""
from datetime import datetime
from typing import Dict, List

from metagpt.schema import Message
from metagpt.logs import logger

from .base_analyzer import BaseAnalyzer
from ..schema import AnalysisMessage
from ..actions.function_actions import AnalyzeFunctionAction

class FunctionAnalyzer(BaseAnalyzer):
    """函数分析器"""
    name: str = "FunctionAnalyzer"
    description: str = "负责分析代码函数"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.set_actions([AnalyzeFunctionAction])
    
    async def _process(self, msg: Message) -> Message:
        """处理函数分析请求"""
        try:
            # 获取结构分析结果
            content = msg.content
            if isinstance(content, str):
                import json
                content = json.loads(content)
                
            structures = content.get('structures', {})
            if not structures:
                raise ValueError("没有找到可分析的结构")
                
            # 分析所有文件中的函数
            analysis_results = {}
            action = AnalyzeFunctionAction()
            
            for file_path, structure in structures.items():
                file_results = {}
                source_code = structure.get('source_code', '')
                
                # 分析全局函数
                for func in structure.get('functions', []):
                    context = {
                        'file_path': file_path,
                        'function_name': func['name'],
                        'start_line': func['start_line'],
                        'end_line': func['end_line'],
                        'source_code': source_code
                    }
                    result = await action.run(context)
                    if 'error' not in result:
                        file_results[func['name']] = result
                        
                # 分析类方法
                for cls in structure.get('classes', []):
                    class_name = cls['name']
                    for method in cls.get('methods', []):
                        method_full_name = f"{class_name}.{method['name']}"
                        context = {
                            'file_path': file_path,
                            'function_name': method_full_name,
                            'start_line': method['start_line'],
                            'end_line': method['end_line'],
                            'source_code': source_code
                        }
                        result = await action.run(context)
                        if 'error' not in result:
                            file_results[method_full_name] = result
                            
                if file_results:
                    analysis_results[file_path] = file_results
                    
            # 更新状态
            result = {
                'functions': analysis_results,
                'total_functions': sum(len(funcs) for funcs in analysis_results.values())
            }
            self.update_state("completed", result=result)
            
            # 发送结果给报告生成器
            return AnalysisMessage(
                role=self.name,
                content=result,
                phase="function_analysis_completed",
                processor={"ReportGenerator"},
                dependencies={"StructureAnalyzer"}
            )
            
        except Exception as e:
            error_msg = f"函数分析失败: {str(e)}"
            logger.error(error_msg)
            self.update_state("error", error=error_msg)
            return AnalysisMessage(
                role=self.name,
                content={"error": error_msg},
                phase="error",
                processor={self.name}
            ) 