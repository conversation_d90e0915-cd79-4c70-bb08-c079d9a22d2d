"""
类归档代码分析器演示
作者: yuanming<PERSON>i
创建时间: 2024-03-09

这个文件演示了优化后的代码分析器的类归档功能，包括：
1. 按类归档存储AST信息
2. 方法调用关系分析
3. 大模型自然语言解释
4. 增量更新支持
"""

import asyncio
from pathlib import Path
from code_analyzer_v2 import analyze_single_file, ClassArchiveManager, AnalyzeClassArchive


async def demo_class_archive():
    """演示类归档功能"""
    print("🚀 类归档代码分析器演示")
    print("=" * 60)
    
    # 分析当前文件
    current_file = __file__
    print(f"📁 正在分析文件: {Path(current_file).name}")
    
    # 使用简化接口分析
    archive = await analyze_single_file(current_file, enable_llm=True)
    
    print(f"\n✅ 分析完成！")
    print(f"📊 发现 {len(archive.classes)} 个类")
    print(f"📄 文件信息: {archive.file_info}")
    
    # 显示每个类的详细信息
    for class_name, class_info in archive.classes.items():
        print(f"\n🏗️  类: {class_name}")
        print(f"   📍 位置: 行 {class_info.start_line}-{class_info.end_line}")
        print(f"   🔧 方法数: {len(class_info.methods)}")
        print(f"   🔗 调用关系: {len(class_info.method_calls)}")
        print(f"   🏷️  继承: {class_info.bases if class_info.bases else '无'}")
        print(f"   📝 文档: {'有' if class_info.docstring else '无'}")
        print(f"   🔍 哈希值: {class_info.hash_value[:8]}...")
        
        # 显示方法列表
        if class_info.methods:
            print(f"   📋 方法列表:")
            for method_name, method_info in class_info.methods.items():
                args = ', '.join(method_info['args'])
                print(f"      • {method_name}({args})")
        
        # 显示方法调用关系
        if class_info.method_calls:
            print(f"   🔄 方法调用:")
            call_types = {}
            for call in class_info.method_calls:
                if call.call_type not in call_types:
                    call_types[call.call_type] = []
                call_types[call.call_type].append(call)
            
            for call_type, calls in call_types.items():
                print(f"      {call_type.upper()}: {len(calls)} 次")
                for call in calls[:2]:  # 只显示前2个
                    print(f"        - {call.caller_method} → {call.called_method}")
        
        # 显示LLM分析结果摘要
        if class_info.analysis_result:
            print(f"   🤖 AI分析: 已完成 ({len(class_info.analysis_result)} 字符)")
    
    return archive


async def demo_archive_management():
    """演示归档管理功能"""
    print(f"\n📦 归档管理演示")
    print("-" * 40)
    
    archive_manager = ClassArchiveManager()
    
    # 查看已保存的归档
    archive_dir = Path("class_archives")
    if archive_dir.exists():
        archive_files = list(archive_dir.glob("*.json"))
        print(f"📁 找到 {len(archive_files)} 个归档文件")
        
        if archive_files:
            # 加载最新的归档
            latest_archive = max(archive_files, key=lambda x: x.stat().st_mtime)
            print(f"📄 加载最新归档: {latest_archive.name}")
            
            archive = archive_manager.load_archive(str(latest_archive))
            print(f"   创建时间: {archive.created_at}")
            print(f"   更新时间: {archive.updated_at}")
            print(f"   包含类数: {len(archive.classes)}")
            
            # 显示类间调用关系
            if archive.call_graph:
                print(f"   🔗 类间调用关系:")
                for caller, callees in archive.call_graph.items():
                    print(f"      {caller} → {', '.join(callees)}")
    else:
        print("📁 归档目录不存在")
    
    # 查看生成的文档
    doc_dirs = list(Path(".").glob("class_docs_*"))
    if doc_dirs:
        print(f"\n📚 找到 {len(doc_dirs)} 个文档目录")
        for doc_dir in doc_dirs:
            md_files = list(doc_dir.glob("*.md"))
            print(f"   📂 {doc_dir.name}: {len(md_files)} 个文档")
    else:
        print("\n📚 未找到文档目录")


class DemoClass:
    """演示类 - 用于展示类归档功能"""
    
    def __init__(self, name: str, config: dict = None):
        """
        初始化演示类
        
        Args:
            name: 类名称
            config: 配置字典
        """
        self.name = name
        self.config = config or {}
        self.data = []
        self.status = "initialized"
    
    def add_item(self, item: str) -> bool:
        """
        添加项目到数据列表
        
        Args:
            item: 要添加的项目
            
        Returns:
            bool: 添加是否成功
        """
        if self._validate_item(item):
            self.data.append(item)
            self._log_operation("add", item)
            return True
        return False
    
    def get_items(self) -> list:
        """
        获取所有项目
        
        Returns:
            list: 项目列表的副本
        """
        self._log_operation("get", f"{len(self.data)} items")
        return self.data.copy()
    
    def process_items(self) -> dict:
        """
        处理所有项目
        
        Returns:
            dict: 处理结果
        """
        # 调用内部方法
        filtered = self._filter_items()
        sorted_items = self._sort_items(filtered)
        
        result = {
            'total': len(self.data),
            'filtered': len(filtered),
            'processed': sorted_items
        }
        
        self._log_operation("process", f"processed {len(sorted_items)} items")
        return result
    
    def _validate_item(self, item: str) -> bool:
        """验证项目是否有效"""
        return isinstance(item, str) and len(item) > 0
    
    def _filter_items(self) -> list:
        """过滤项目"""
        return [item for item in self.data if len(item) > 2]
    
    def _sort_items(self, items: list) -> list:
        """排序项目"""
        return sorted(items)
    
    def _log_operation(self, operation: str, details: str) -> None:
        """记录操作日志"""
        print(f"[{self.name}] {operation}: {details}")


class AdvancedDemo(DemoClass):
    """高级演示类 - 继承自DemoClass"""
    
    def __init__(self, name: str, config: dict = None, advanced_mode: bool = True):
        """
        初始化高级演示类
        
        Args:
            name: 类名称
            config: 配置字典
            advanced_mode: 是否启用高级模式
        """
        super().__init__(name, config)
        self.advanced_mode = advanced_mode
        self.cache = {}
    
    def advanced_process(self) -> dict:
        """
        高级处理方法
        
        Returns:
            dict: 高级处理结果
        """
        # 调用父类方法
        basic_result = self.process_items()
        
        # 执行高级处理
        if self.advanced_mode:
            enhanced_data = self._enhance_data(basic_result['processed'])
            cached_result = self._cache_result(enhanced_data)
            
            return {
                **basic_result,
                'enhanced': enhanced_data,
                'cached': cached_result
            }
        
        return basic_result
    
    def _enhance_data(self, items: list) -> list:
        """增强数据处理"""
        return [f"enhanced_{item}" for item in items]
    
    def _cache_result(self, data: list) -> bool:
        """缓存结果"""
        cache_key = f"result_{len(data)}"
        self.cache[cache_key] = data
        return True


async def main():
    """主演示函数"""
    print("🎯 开始类归档代码分析器演示")
    
    # 1. 演示类归档功能
    archive = await demo_class_archive()
    
    # 2. 演示归档管理
    await demo_archive_management()
    
    print(f"\n🎉 演示完成！")
    print("💡 提示:")
    print("   - 查看生成的 class_docs_* 目录获取详细文档")
    print("   - 查看 class_archives 目录获取JSON归档文件")
    print("   - 每个类都有独立的Markdown文档和LLM分析")


if __name__ == "__main__":
    asyncio.run(main())
