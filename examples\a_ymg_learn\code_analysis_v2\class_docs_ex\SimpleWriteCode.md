# 类: SimpleWriteCode

**文件路径**: d:\bigModel\curser\stock_test1\backend\MetaGPT\examples\a_ymg_learn\ex.py
**行号范围**: 38-53
**最后更新**: 2025-05-27 09:53:30
**继承**: Action

## 方法列表
### run
- **行号**: 46-53
- **参数**: self, instruction

## 方法调用关系
### STATIC 调用
- SimpleWriteCode.run → parse_code (行 51)
### SELF 调用
- SimpleWriteCode.run → SimpleWriteCode._aask (行 49)

## 源代码
```python
class SimpleWriteCode(Action):
    PROMPT_TEMPLATE: str = """
    Write a python function that can {instruction}.
    Return ```python your_code_here ``` with NO other texts,
    your code:
    """
    name: str = "SimpleWriteCode"

    async def run(self, instruction: str):
        prompt = self.PROMPT_TEMPLATE.format(instruction=instruction)

        rsp = await self._aask(prompt)

        code_text = parse_code(rsp)

        return code_text
```

## 智能分析
分析出错: Error communicating with LLM