"""
文件名: MetaGPT/examples/a_ymg_learn/documents_analysis/documents_checker.py
创建日期: 2024年
描述: 实现基于MetaGPT的需求文档检查系统，包含三个专家角色协作完成任务
"""

import asyncio
import platform
import os
from typing import Any, Dict, List, Set, Optional

import fire
import json

from metagpt.actions import Action, UserRequirement
from metagpt.logs import logger
from metagpt.roles import Role
from metagpt.schema import Message
from metagpt.team import Team


class ParseDocument(Action):
    """动作：文档解析专家解析文档"""

    PROMPT_TEMPLATE: str = """
    ## 背景介绍
    你是文档解析专家，需要解析用户提供的需求文档。

    ## 用户文档
    {document_content}

    ## 文档解析任务
    请详细分析上述文档，提取以下内容：
    1. 文档的主要结构和组织方式
    2. 文档中描述的功能需求（详细列出每一条）
    3. 文档中描述的非功能需求（如性能、安全、可用性等）
    4. 文档中的关键术语及其定义
    5. 文档中的约束条件（时间、资源、技术等限制）
    6. 文档中提到的具体截止时间（精确到具体日期）
    7. 文档中的系统接口定义
    8. 文档中的用户界面要求
    9. 文档中的法律法规要求
    10. 文档中的需求优先级划分

    特别提示：
    - 对于日期和时间信息，请特别注意准确提取，例如"2025年4月10日前完成"这样的表述。
    - 如果文档中某项内容不存在，请明确标注为"不存在"，不要使用空数组或空对象。
    - 请基于文档的实际内容进行解析，不要添加自己的假设或推断。
    - 返回的JSON必须是有效的格式，不要包含注释或其他非JSON元素。
    - 不要在JSON中使用占位符（如"..."、"等"）。每个列表必须明确列出所有项目，或者明确标明"不存在"。

    请以JSON格式返回解析结果，保证格式的有效性，不要包含任何非JSON内容。不要使用markdown代码块，直接返回纯JSON字符串。

    示例输出格式（注意这只是格式示例，实际内容应根据你的解析结果填充）：
    {{
      "document_structure": "文档分为X个部分，包括引言、功能需求、非功能需求...",
      "functional_requirements": ["系统应提供用户登录功能", "系统应支持数据导出功能"],
      "non_functional_requirements": ["系统响应时间不超过2秒", "系统可用性应达到99.9%"],
      "key_terms": {{"API": "应用程序接口", "UI": "用户界面"}},
      "constraints": ["项目必须在2025年前完成", "开发预算不超过100万元"],
      "deadlines": ["2025年4月10日前完成系统开发", "2024年12月前完成原型设计"],
      "interfaces": ["RESTful API接口", "数据库接口"],
      "ui_requirements": ["界面应遵循Material Design设计规范", "支持暗黑模式"],
      "legal_requirements": ["符合GDPR数据保护要求", "遵守国内相关隐私法规"],
      "priority": ["高优先级: 用户登录功能", "中优先级: 数据导出功能"]
    }}

    如果某些内容在文档中不存在，请这样标注：
    {{
      "document_structure": "文档分为简介和系统功能两部分",
      "functional_requirements": ["用户注册功能", "内容搜索功能"],
      "non_functional_requirements": "不存在",
      "key_terms": {{"用户": "系统的使用者"}},
      "constraints": "不存在",
      "deadlines": "不存在",
      "interfaces": "不存在",
      "ui_requirements": "不存在",
      "legal_requirements": "不存在",
      "priority": "不存在"
    }}

    重要提示：只返回纯JSON格式，不要有任何额外说明，也不要使用```json和```这样的标记。请确保你的JSON是有效的，所有字符串都用双引号，不要使用任何占位符或省略号。
    """
    name: str = "ParseDocument"

    async def run(self, document_content: str):
        """执行文档解析

        Args:
            document_content: 需求文档内容

        Returns:
            str: 解析后的文档结构（JSON格式字符串）
        """
        prompt = self.PROMPT_TEMPLATE.format(document_content=document_content)
        logger.info(f"文档解析提示: {prompt}")
        
        try:
            # 尝试多次解析，如果失败则重试
            max_attempts = 2
            for attempt in range(max_attempts):
                try:
                    # 请求AI解析文档
                    rsp = await self._aask(prompt)
                    
                    # 记录返回的前100个字符，用于调试
                    logger.info(f"AI返回结果前100个字符: {rsp[:100] if rsp else '空'}")
                    
                    # 处理返回结果，确保是有效的JSON
                    if not rsp or rsp.strip() == "":
                        logger.warning("AI返回了空结果，使用空JSON对象")
                        return "{}"
                    
                    # 预处理：清理常见的非JSON内容
                    clean_rsp = rsp.strip()
                    
                    # 1. 移除Markdown代码块标记
                    # 处理 ```json 或 ``` 开头的情况
                    if clean_rsp.startswith("```"):
                        # 找到第一个和最后一个 ``` 标记
                        start_pos = clean_rsp.find("```")
                        # 检查是否有语言标识
                        if "json" in clean_rsp[start_pos:start_pos+10].lower():
                            # 跳过 ```json
                            start_pos = clean_rsp.find("\n", start_pos) + 1
                        else:
                            # 跳过 ```
                            start_pos = start_pos + 3
                            # 如果下一行是language标记，再跳过一行
                            if clean_rsp[start_pos:].strip().startswith("json"):
                                start_pos = clean_rsp.find("\n", start_pos) + 1
                        
                        # 找到最后一个 ```
                        end_pos = clean_rsp.rfind("```")
                        if end_pos > start_pos:
                            clean_rsp = clean_rsp[start_pos:end_pos].strip()
                            logger.info("从markdown代码块中提取了JSON内容")
                        else:
                            # 可能只有开始的```但没有结束的```
                            clean_rsp = clean_rsp[start_pos:].strip()
                            logger.info("从不完整markdown代码块中提取了JSON内容")
                    
                    # 2. 查找JSON的实际开始位置（找到第一个 { 字符）
                    json_start = clean_rsp.find("{")
                    if json_start != -1:
                        # 检查开始之前是否有非JSON内容
                        if json_start > 0:
                            pre_content = clean_rsp[:json_start].strip()
                            if pre_content:
                                logger.info(f"删除JSON前的内容: {pre_content[:50]}...")
                            clean_rsp = clean_rsp[json_start:]
                    
                        # 3. 查找JSON的实际结束位置（找到最后一个 } 字符）
                        json_end = clean_rsp.rfind("}")
                        if json_end != -1 and json_end < len(clean_rsp) - 1:
                            post_content = clean_rsp[json_end+1:].strip()
                            if post_content:
                                logger.info(f"删除JSON后的内容: {post_content[:50]}...")
                            clean_rsp = clean_rsp[:json_end+1]
                    
                    # 4. 检查并移除占位符
                    if "..." in clean_rsp:
                        logger.warning("检测到JSON中包含占位符'...'，尝试修复")
                        # 检查是否在数组中
                        import re
                        
                        # 标准数组模式：["item1", "item2", "..."]，替换为["item1", "item2"]
                        clean_rsp = re.sub(r'(\[(?:"[^"]*",\s*)*)"[^"]*\.\.\.(?:[^"]*)"(,?\s*\])', r'\1\2', clean_rsp)
                        
                        # 完全空的数组模式：["..."]，替换为[]
                        clean_rsp = re.sub(r'\[\s*"\.\.\."\s*\]', '[]', clean_rsp)
                        
                        # 单项数组中的省略号：["item1 等..."]，替换为["item1"]
                        clean_rsp = re.sub(r'"([^"]*)等\.\.\.(?:[^"]*)"', r'"\1"', clean_rsp)
                        
                        # 如果还有其他占位符，替换整个字段
                        if "..." in clean_rsp:
                            logger.warning("尝试替换所有包含占位符的字段为空数组或不存在")
                            # 把带...的字符串替换为 "不存在"
                            clean_rsp = re.sub(r'"[^"]*\.\.\.(?:[^"]*)"', '"不存在"', clean_rsp)
                            # 把带...的数组替换为空数组
                            clean_rsp = re.sub(r'\[\s*(?:"[^"]*",\s*)?"[^"]*\.\.\.(?:[^"]*)"\s*(?:,\s*"[^"]*")?\s*\]', '[]', clean_rsp)
                    
                    # 5. 尝试解析JSON
                    try:
                        parsed_json = json.loads(clean_rsp)
                        
                        # 6. 检查每个字段，确保没有占位符
                        for key, value in parsed_json.items():
                            if isinstance(value, str) and ("..." in value or "等" in value):
                                parsed_json[key] = "不存在"
                            elif isinstance(value, list):
                                # 检查每个列表项
                                cleaned_list = []
                                has_placeholder = False
                                for item in value:
                                    if isinstance(item, str) and ("..." in item or "等" in item):
                                        has_placeholder = True
                                    else:
                                        cleaned_list.append(item)
                                
                                if has_placeholder:
                                    # 如果找到了占位符但还有其他有效项，使用清理后的列表
                                    if cleaned_list:
                                        parsed_json[key] = cleaned_list
                                    else:
                                        # 如果所有项都有问题，设为"不存在"
                                        parsed_json[key] = "不存在"
                        
                        # 成功解析并清理JSON
                        return json.dumps(parsed_json, ensure_ascii=False)
                    
                    except json.JSONDecodeError as je:
                        logger.error(f"JSON解析失败: {je}, 位置: {je.pos}, 行: {je.lineno}, 列: {je.colno}")
                        logger.error(f"问题文本: {clean_rsp[max(0, je.pos-30):min(len(clean_rsp), je.pos+30)]}")
                        
                        # 如果是最后一次尝试，则执行更深层次的修复
                        if attempt == max_attempts - 1:
                            try:
                                # 1. 修复常见的JSON语法问题
                                fixed_json = clean_rsp
                                # 替换单引号为双引号
                                fixed_json = fixed_json.replace("'", '"')
                                # 替换JS风格注释
                                import re
                                fixed_json = re.sub(r'//.*?\n', '\n', fixed_json)
                                fixed_json = re.sub(r'/\*.*?\*/', '', fixed_json, flags=re.DOTALL)
                                
                                # 移除占位符 "..."
                                fixed_json = fixed_json.replace('"..."', '"不存在"')
                                fixed_json = re.sub(r'\[\s*"\.\.\."\s*\]', '[]', fixed_json)
                                # 移除尾部可能残留的逗号
                                fixed_json = re.sub(r',\s*}', '}', fixed_json)
                                fixed_json = re.sub(r',\s*\]', ']', fixed_json)
                                
                                # 2. 处理可能的Unicode问题
                                try:
                                    # 检查BOM标记
                                    if fixed_json.startswith('\ufeff'):
                                        fixed_json = fixed_json[1:]
                                        logger.info("移除了BOM标记")
                                except:
                                    pass
                                
                                # 3. 尝试解析修复后的JSON
                                try:
                                    parsed_json = json.loads(fixed_json)
                                    logger.info("成功修复并解析JSON")
                                    return json.dumps(parsed_json, ensure_ascii=False)
                                except json.JSONDecodeError:
                                    # 如果还是失败，尝试更极端的方法
                                    logger.warning("常规修复失败，尝试更极端的JSON修复方法")
                                    
                                    # 尝试用正则表达式提取可能的JSON部分
                                    import re
                                    # 寻找可能的完整JSON对象
                                    json_pattern = r'(\{.*\})'
                                    matches = re.search(json_pattern, fixed_json, re.DOTALL)
                                    if matches:
                                        potential_json = matches.group(1)
                                        try:
                                            parsed_json = json.loads(potential_json)
                                            logger.info("通过正则表达式提取并成功解析JSON")
                                            return json.dumps(parsed_json, ensure_ascii=False)
                                        except:
                                            pass
                                    
                                    # 最后手段：构建一个基本的JSON对象
                                    return self._create_fallback_json(document_content)
                            except Exception as repair_err:
                                logger.error(f"尝试修复JSON失败: {str(repair_err)}")
                                # 返回一个基本的JSON对象
                                return self._create_fallback_json(document_content)
                        else:
                            logger.warning(f"第 {attempt+1} 次解析失败，将重试")
                
                except Exception as e:
                    logger.error(f"解析文档过程中发生错误: {str(e)}")
                    if attempt == max_attempts - 1:
                        import traceback
                        logger.error(f"异常详情: {traceback.format_exc()}")
                        # 返回一个基本的JSON对象
                        return self._create_fallback_json(document_content)
                    else:
                        logger.warning(f"第 {attempt+1} 次解析出错，将重试")
            
            # 如果所有尝试都失败
            return self._create_fallback_json(document_content)
            
        except Exception as e:
            logger.error(f"解析文档过程中发生未预期错误: {str(e)}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            # 返回一个基本的JSON对象
            return self._create_fallback_json(document_content)
    
    def _create_fallback_json(self, document_content: str) -> str:
        """创建一个基本的JSON对象，当所有解析尝试都失败时使用
        
        Args:
            document_content: 原始文档内容
            
        Returns:
            str: 基本的JSON字符串
        """
        logger.warning("创建基本的JSON结构作为备选")
        
        # 提取文档的前200个字符作为文档结构描述
        doc_preview = document_content[:200] + ("..." if len(document_content) > 200 else "")
        
        fallback_json = {
            "document_structure": f"文档解析失败，内容开头为: {doc_preview}",
            "functional_requirements": "解析失败",
            "non_functional_requirements": "解析失败",
            "key_terms": {},
            "constraints": "解析失败",
            "deadlines": "解析失败",
            "interfaces": "解析失败",
            "ui_requirements": "解析失败",
            "legal_requirements": "解析失败",
            "priority": "解析失败"
        }
        
        return json.dumps(fallback_json, ensure_ascii=False)


class CheckDocumentPoint(Action):
    """动作：检查专家检查文档是否符合特定检查点的要求"""

    PROMPT_TEMPLATE: str = """
    ## 背景介绍
    你是文档检查专家，需要根据特定检查点评估解析后的需求文档。

    ## 解析后的文档
    {parsed_document}

    ## 原始文档
    {original_document}

    ## 检查点
    {check_point}

    ## 检查指南
    在评估文档是否满足检查点要求时，请遵循以下标准：
    
    1. 满足：文档完全符合检查点要求，包含所有必要信息且格式正确。
       - 例如：如果检查点是"是否包含具体的截止时间"，则文档中必须明确包含具体日期时间（如"2025年4月10日前"）。
    
    2. 部分满足：文档包含了相关信息，但不完整或不够明确。
       - 例如：文档提到了时间范围但没有具体日期，或者只在某些地方提到了时间要求。
    
    3. 不满足：文档完全没有包含相关信息，或者所包含的信息与检查点要求完全不符。
       - 例如：文档中完全没有提到任何时间相关信息。
    
    重要提示：
    - 在评估前，请仔细阅读原始文档全文，确保不会遗漏任何相关信息。
    - 如果文档中确实包含了特定信息（如日期"2025年4月10日"），请勿在评估中声明该信息不存在。
    - 在给出改进建议时，请确保建议与你的评估结果一致，不要建议修改已经存在的内容。
    - 评估结果必须基于文档的实际内容，而非你认为文档应该包含的内容。
    - 如遇到边界情况，请优先考虑部分满足而非完全不满足。
    - 特别注意：不要将检查点定义中的"示例"误认为是被检查文档中的实际内容。你的评估必须只基于"原始文档"部分的内容。
    - 如果在解析后的文档中看到"check_point_definition"部分，这只是检查标准，不是文档的一部分，不要在评估中引用它作为文档内容。

    ## 检查任务
    请评估上述文档是否满足给定的检查点要求，并提供详细说明和证据。
    请使用以下格式回答：

    检查点：[检查点内容]
    评估结果：[满足/不满足/部分满足]
    详细说明：[说明文档是否满足该检查点，以及满足或不满足的具体证据，必须引用文档中的实际内容，不要引用检查点定义中的示例作为文档内容]
    改进建议：[如何改进文档以更好地满足该检查点的要求]
    """
    name: str = "CheckDocumentPoint"

    async def run(self, parsed_document: str, original_document: str, check_point: str):
        """执行文档检查

        Args:
            parsed_document: 解析后的文档结构
            original_document: 原始文档内容
            check_point: 待检查的检查点

        Returns:
            str: 检查结果
        """
        # 检查parsed_document是否表示解析失败
        try:
            parsed_json = json.loads(parsed_document)
            parse_failed = False
            
            # 记录原始文档的前100个字符，用于后续提示
            original_preview = original_document[:100] + "..." if len(original_document) > 100 else original_document
            logger.info(f"原始文档内容预览: {original_preview}")
            
            # 检查主要字段是否都是"解析失败"
            failure_indicators = ["解析失败", "解析过程出错", "文档解析失败"]
            failure_count = 0
            
            for value in parsed_json.values():
                if isinstance(value, str) and any(indicator in value for indicator in failure_indicators):
                    failure_count += 1
            
            # 如果大部分字段都表示失败，则认为解析失败
            if failure_count > len(parsed_json) / 2:
                parse_failed = True
                logger.warning(f"解析文档显示大多数字段解析失败，将直接使用原始文档进行评估")
            
            # 如果document_structure特别表明了解析失败
            if isinstance(parsed_json.get("document_structure"), str) and any(indicator in parsed_json["document_structure"] for indicator in failure_indicators):
                parse_failed = True
                logger.warning(f"文档结构解析失败，将直接使用原始文档进行评估")
                
            if parse_failed:
                # 如果解析失败，使用一个简化的提示模板
                simplified_prompt = f"""
                ## 背景介绍
                你是文档检查专家，需要根据特定检查点评估需求文档。
                
                ## 原始文档
                {original_document}
                
                ## 检查点
                {check_point}
                
                ## 重要提示
                - 你的评估必须仅基于上述"原始文档"部分的实际内容
                - 不要将检查点中可能包含的示例误认为是文档中的实际内容
                - 如果文档中完全没有提到某项内容，请如实说明，不要臆想不存在的内容
                
                ## 检查任务
                请直接评估原始文档是否满足给定的检查点要求，并提供详细说明和证据。
                请使用以下格式回答：
                
                检查点：[检查点内容]
                评估结果：[满足/不满足/部分满足]
                详细说明：[说明文档是否满足该检查点，以及满足或不满足的具体证据，必须引用文档中的实际内容]
                改进建议：[如何改进文档以更好地满足该检查点的要求]
                """
                rsp = await self._aask(simplified_prompt)
                return rsp
        except json.JSONDecodeError:
            logger.error(f"无法解析 parsed_document 为JSON，将直接使用原始文档")
            # 如果解析失败，使用一个简化的提示模板
            simplified_prompt = f"""
            ## 背景介绍
            你是文档检查专家，需要根据特定检查点评估需求文档。
            
            ## 原始文档
            {original_document}
            
            ## 检查点
            {check_point}
            
            ## 重要提示
            - 你的评估必须仅基于上述"原始文档"部分的实际内容
            - 不要将检查点中可能包含的示例误认为是文档中的实际内容
            - 如果文档中完全没有提到某项内容，请如实说明，不要臆想不存在的内容
            
            ## 检查任务
            请直接评估原始文档是否满足给定的检查点要求，并提供详细说明和证据。
            请使用以下格式回答：
            
            检查点：[检查点内容]
            评估结果：[满足/不满足/部分满足]
            详细说明：[说明文档是否满足该检查点，以及满足或不满足的具体证据，必须引用文档中的实际内容]
            改进建议：[如何改进文档以更好地满足该检查点的要求]
            """
            rsp = await self._aask(simplified_prompt)
            return rsp
        
        # 正常情况下执行完整的评估
        prompt = self.PROMPT_TEMPLATE.format(
            parsed_document=parsed_document,
            original_document=original_document,
            check_point=check_point
        )
        rsp = await self._aask(prompt)
        return rsp


class SummarizeDocument(Action):
    """动作：文档总结专家汇总检查结果"""

    PROMPT_TEMPLATE: str = """
    ## 背景介绍
    你是文档总结专家，需要汇总所有检查点的评估结果，生成最终报告。

    ## 检查结果
    {check_results}

    ## 总结任务
    请根据以上检查结果，生成一份综合评估报告。报告应包含以下内容：
    1. 总体评估概述
    2. 已满足的检查点列表
    3. 未满足的检查点列表
    4. 部分满足的检查点列表
    5. 具体的改进建议

    请使用以下格式回答：

    # 需求文档检查报告

    ## 总体评估
    [总体评估内容]

    ## 满足的检查点
    - [检查点1]
    - [检查点2]
    ...

    ## 未满足的检查点
    - [检查点1]：[简短说明为什么未满足]
    - [检查点2]：[简短说明为什么未满足]
    ...

    ## 部分满足的检查点
    - [检查点1]：[简短说明为什么部分满足]
    - [检查点2]：[简短说明为什么部分满足]
    ...

    ## 改进建议
    1. [建议1]
    2. [建议2]
    ...
    """
    name: str = "SummarizeDocument"

    async def run(self, check_results: List[str]):
        """执行文档总结

        Args:
            check_results: 所有检查点的检查结果列表

        Returns:
            str: 综合评估报告
        """
        # 将检查结果列表转换为字符串
        check_results_str = "\n\n".join(check_results)
        prompt = self.PROMPT_TEMPLATE.format(check_results=check_results_str)
        rsp = await self._aask(prompt)
        return rsp


class DocumentParsingExpert(Role):
    """文档解析专家角色"""
    name: str = "DocumentParsingExpert"
    profile: str = "文档解析专家"
    next_role: str = "DocumentCheckingExpert"

    def __init__(self, **data: Any):
        super().__init__(**data)
        self.set_actions([ParseDocument])
        self._watch([UserRequirement])
        # 存储解析后的文档和检查点数据
        self.parsed_document = ""
        self.original_document = ""
        self.check_points = []
        self.current_check_point_index = 0

    async def _act(self) -> Message:
        """执行文档解析动作，并将结果传递给检查专家"""
        logger.info(f"{self._setting}: 执行 {self.rc.todo}({self.rc.todo.name})")
        todo = self.rc.todo  # ParseDocument 实例

        # 检查是否是来自检查专家的消息
        checking_msgs = [msg for msg in self.rc.news if msg.sent_from == "DocumentCheckingExpert"]
        if checking_msgs:
            # 已完成一个检查点，处理下一个
            last_msg = checking_msgs[-1]
            # 检查消息内容是否包含"跳过重复检查点"
            if "（跳过重复检查点" in last_msg.content:
                logger.info(f"收到跳过重复检查点的通知: {last_msg.content}")
                # 提取被跳过的检查点名称（如果有）
                import re
                match = re.search(r'（跳过重复检查点:\s*(.*?)）', last_msg.content)
                if match:
                    skipped_checkpoint = match.group(1)
                    logger.info(f"跳过重复检查点: {skipped_checkpoint}")
            
            self.current_check_point_index += 1
            logger.info(f"处理下一个检查点，当前索引: {self.current_check_point_index}/{len(self.check_points)}")
        else:
            # 首次调用，解析文档和检查点
            memories = self.get_memories()
            logger.info(f"文档解析专家记忆: {memories}")
            
            # 从记忆中获取最新的消息内容
            user_requirement = memories[-1]
            document_content = user_requirement.content
            
            # 获取检查点列表（从传入的参数或使用默认值）
            if hasattr(self, 'check_points_list') and self.check_points_list:
                # 确保检查点列表中没有重复项
                unique_check_points = []
                seen_check_points = set()
                
                for cp in self.check_points_list:
                    if cp not in seen_check_points:
                        unique_check_points.append(cp)
                        seen_check_points.add(cp)
                    else:
                        logger.warning(f"检查点列表中存在重复项: {cp}")
                
                self.check_points = unique_check_points
                logger.info(f"使用自定义检查点列表，去重后共有 {len(self.check_points)} 个检查点")
            else:
                # 使用默认检查点列表
                self.check_points = [
                    "文档是否包含明确的功能需求",
                    "文档是否包含非功能需求",
                    "文档是否定义了关键术语",
                    "文档是否包含约束条件",
                    "文档结构是否清晰合理"
                ]
                logger.info(f"使用默认检查点列表，共有 {len(self.check_points)} 个检查点")
            
            # 重置当前检查点索引
            self.current_check_point_index = 0
            
            # 执行文档解析
            self.parsed_document = await todo.run(document_content=document_content)
            self.original_document = document_content
            logger.info(f"文档已解析，共有 {len(self.check_points)} 个检查点需要检查")
        
        # 如果所有检查点都已处理完毕，则返回完成消息
        if self.current_check_point_index >= len(self.check_points):
            logger.info("所有检查点已处理完毕")
            return Message(
                content="所有检查点已处理完毕",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={self.next_role},
            )
        
        # 获取当前需要处理的检查点
        current_check_point = self.check_points[self.current_check_point_index]
        
        # 创建传递给检查专家的消息，包含当前检查点和解析后的文档
        msg_content = {
            "check_point": current_check_point,
            "check_point_index": self.current_check_point_index,
            "total_check_points": len(self.check_points),
            "parsed_document": self.parsed_document,
            "original_document": self.original_document
        }
        
        msg = Message(
            content=json.dumps(msg_content),
            role=self.profile,
            cause_by=type(todo),
            sent_from=self.name,
            send_to={self.next_role},
        )
        self.rc.memory.add(msg)

        return msg


class DocumentCheckingExpert(Role):
    """文档检查专家角色"""
    name: str = "DocumentCheckingExpert"
    profile: str = "文档检查专家"
    next_role: str = "DocumentSummaryExpert"

    def __init__(self, **data: Any):
        super().__init__(**data)
        self.set_actions([CheckDocumentPoint])
        self._watch([ParseDocument])
        # 存储检查结果
        self.check_results = []
        # 存储输出文件路径
        self.output_file_path = data.get("output_file_path", "document_check_report.md")
        # 存储检查点定义
        self.check_points_definitions = []

    async def _observe(self) -> int:
        """观察来自文档解析专家的消息"""
        await super()._observe()
        # 只接收发送给自己的消息
        self.rc.news = [msg for msg in self.rc.news if self.name in msg.send_to]
        return len(self.rc.news)
    
    def _get_check_point_definition(self, check_point: str) -> Dict:
        """根据检查点名称获取其定义

        Args:
            check_point: 检查点名称

        Returns:
            Dict: 检查点定义，如果没有找到则返回空字典
        """
        if not self.check_points_definitions:
            return {}
            
        # 尝试通过名称匹配
        for definition in self.check_points_definitions:
            if definition.get("name") == check_point:
                return definition
                
        # 如果没有精确匹配，尝试部分匹配
        for definition in self.check_points_definitions:
            if check_point in definition.get("name", "") or definition.get("name", "") in check_point:
                return definition
                
        return {}

    async def _act(self) -> Message:
        """执行文档检查动作，并将结果传递给总结专家"""
        logger.info(f"{self._setting}: 执行 {self.rc.todo}({self.rc.todo.name})")
        todo = self.rc.todo  # CheckDocumentPoint 实例

        # 获取来自文档解析专家的消息
        doc_parsing_msgs = [msg for msg in self.rc.news if msg.sent_from == "DocumentParsingExpert"]
        if not doc_parsing_msgs:
            logger.warning("未收到文档解析专家的消息")
            return Message(
                content="未收到文档解析专家的消息",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={self.next_role},
            )
        
        # 获取最新消息
        latest_msg = doc_parsing_msgs[-1]
        
        # 如果收到的是完成消息，则将结果传递给总结专家
        if latest_msg.content == "所有检查点已处理完毕":
            # 创建传递给总结专家的消息，包含所有检查结果
            msg = Message(
                content=json.dumps({"check_results": self.check_results}),
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={self.next_role},
            )
            self.rc.memory.add(msg)
            return msg
        
        try:
            # 解析消息内容（JSON格式）
            message_content = json.loads(latest_msg.content)
            
            # 获取当前检查点和文档内容
            check_point = message_content.get("check_point", "")
            check_point_index = message_content.get("check_point_index", 0)
            total_check_points = message_content.get("total_check_points", 0)
            parsed_document = message_content.get("parsed_document", "{}")
            original_document = message_content.get("original_document", "")
            
            if not check_point:
                logger.warning("消息中未包含检查点信息")
                return Message(
                    content="（跳过重复检查点）",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"DocumentParsingExpert"},
                )
            
            # 更精确地检查该检查点是否已经被处理过
            # 维护一个已处理过的检查点集合
            if not hasattr(self, 'processed_check_points'):
                self.processed_check_points = set()
                
            # 如果检查点已经处理过，跳过
            if check_point in self.processed_check_points:
                logger.warning(f"检查点 '{check_point}' 已经被处理过，跳过")
                return Message(
                    content=f"（跳过重复检查点: {check_point}）",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"DocumentParsingExpert"},
                )
            
            # 获取检查点定义（如果有）
            check_point_definition = self._get_check_point_definition(check_point)
            if check_point_definition:
                # 如果有检查点定义，添加到parsed_document中
                parsed_json = json.loads(parsed_document) if parsed_document else {}
                parsed_json["check_point_definition"] = check_point_definition
                parsed_document = json.dumps(parsed_json, ensure_ascii=False)
                logger.info(f"为检查点 {check_point} 添加了标准定义")
            
            # 执行文档检查
            logger.info(f"执行检查点 {check_point_index+1}/{total_check_points}: {check_point}")
            check_result = await todo.run(
                parsed_document=parsed_document,
                original_document=original_document,
                check_point=check_point
            )
            
            # 添加检查结果到列表
            self.check_results.append(check_result)
            
            # 标记该检查点已处理
            self.processed_check_points.add(check_point)
            
            # 将检查结果保存到报告文件
            try:
                with open(self.output_file_path, 'a', encoding='utf-8') as f:
                    f.write(f"## 检查点 {check_point_index+1}: {check_point}\n\n")
                    f.write(f"{check_result}\n\n")
                logger.info(f"已将检查点 {check_point_index+1} 的结果保存到 {self.output_file_path}")
            except Exception as e:
                logger.error(f"保存检查点结果到文件时出错: {e}")
            
            # 返回处理下一个检查点的消息
            return Message(
                content=f"已完成检查点评估: {check_point}",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={"DocumentParsingExpert"},
            )
            
        except Exception as e:
            logger.error(f"处理检查点时出错: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            # 创建错误消息，跳过当前检查点
            return Message(
                content=f"处理检查点时出错: {e}",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={"DocumentParsingExpert"},
            )


class DocumentSummaryExpert(Role):
    """文档总结专家角色"""
    name: str = "DocumentSummaryExpert"
    profile: str = "文档总结专家"

    def __init__(self, **data: Any):
        super().__init__(**data)
        self.set_actions([SummarizeDocument])
        self._watch([CheckDocumentPoint])
        # 存储输出文件路径
        self.output_file_path = data.get("output_file_path", "document_check_report.md")

    async def _observe(self) -> int:
        """观察来自检查专家的消息"""
        await super()._observe()
        # 只接收发送给自己的消息
        self.rc.news = [msg for msg in self.rc.news if self.name in msg.send_to]
        return len(self.rc.news)

    async def _act(self) -> Message:
        """执行文档总结动作，生成最终报告"""
        logger.info(f"{self._setting}: 执行 {self.rc.todo}({self.rc.todo.name})")
        todo = self.rc.todo  # SummarizeDocument 实例

        # 获取来自检查专家的消息
        check_msgs = [msg for msg in self.rc.news if msg.sent_from == "DocumentCheckingExpert"]
        if not check_msgs:
            logger.warning("未收到文档检查专家的消息")
            return Message(
                content="未收到文档检查专家的消息",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to=set(),  # 最终结果不需要发送给其他角色
            )
        
        # 从最新消息中获取检查结果
        latest_msg = check_msgs[-1]
        
        try:
            # 解析消息内容（JSON格式）
            message_content = json.loads(latest_msg.content)
            check_results = message_content.get("check_results", [])
            
            if not check_results:
                logger.warning("没有检查结果可供总结")
                return Message(
                    content="没有检查结果可供总结",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to=set(),  # 最终结果不需要发送给其他角色
                )
            
            # 执行文档总结
            summary = await todo.run(check_results=check_results)
            
            # 将总结添加到报告文件末尾
            try:
                with open(self.output_file_path, 'a', encoding='utf-8') as f:
                    f.write(f"# 总结报告\n\n")
                    f.write(f"{summary}\n")
                logger.info(f"已将总结报告保存到 {self.output_file_path}")
            except Exception as e:
                logger.error(f"保存总结报告到文件时出错: {e}")
            
            # 创建最终报告消息
            msg = Message(
                content=summary,
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to=set(),  # 最终结果不需要发送给其他角色
            )
            self.rc.memory.add(msg)

            return msg
            
        except Exception as e:
            logger.error(f"处理总结时出错: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            # 创建错误消息
            return Message(
                content=f"生成总结报告时出错: {e}",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to=set(),  # 最终结果不需要发送给其他角色
            )


async def check_requirements_document(document_content: str, check_points: List[str], output_file_path: str = "document_check_report.md", investment: float = 3.0, n_round: int = 15, check_points_definition_path: Optional[str] = None):
    """运行一个由三个专家组成的团队，协作检查需求文档

    Args:
        document_content: 需求文档内容
        check_points: 检查点列表
        output_file_path: 检查报告输出文件路径
        investment: 团队投资金额
        n_round: 最大运行轮数
        check_points_definition_path: 检查点定义文件路径，用于提供更精确的检查标准
    """
    # 创建三个专家角色
    parsing_expert = DocumentParsingExpert()
    checking_expert = DocumentCheckingExpert(output_file_path=output_file_path)
    summary_expert = DocumentSummaryExpert(output_file_path=output_file_path)
    
    # 设置检查点列表给解析专家
    parsing_expert.check_points_list = check_points
    
    # 加载检查点定义（如果提供）
    if check_points_definition_path:
        if os.path.exists(check_points_definition_path):
            try:
                # 读取文件内容
                with open(check_points_definition_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 记录文件前100个字符用于调试
                logger.info(f"检查点定义文件前100个字符: {content[:100] if content else '空'}")
                
                # 预处理内容，处理常见问题
                if content:
                    # 处理BOM标记
                    if content.startswith('\ufeff'):
                        content = content[1:]
                        logger.info("移除了BOM标记")
                    
                    # 检查文件是否以JSON格式开始
                    content = content.strip()
                    if not content.startswith('[') and not content.startswith('{'):
                        # 尝试查找JSON的实际开始
                        json_start = content.find('[')
                        if json_start == -1:
                            json_start = content.find('{')
                        
                        if json_start != -1:
                            content = content[json_start:]
                            logger.info(f"截取了JSON内容，跳过了前 {json_start} 个字符")
                    
                    try:
                        # 解析JSON内容
                        check_points_definitions = json.loads(content)
                        
                        # 验证检查点定义的格式
                        if isinstance(check_points_definitions, dict):
                            # 如果是字典，将其转换为列表
                            logger.info("检查点定义是字典格式，转换为列表")
                            check_points_definitions = [check_points_definitions]
                        elif not isinstance(check_points_definitions, list):
                            logger.error(f"检查点定义格式错误：应该是列表或字典，实际是 {type(check_points_definitions).__name__}")
                            check_points_definitions = []
                        
                        # 将检查点定义传递给检查专家
                        checking_expert.check_points_definitions = check_points_definitions
                        logger.info(f"已加载检查点定义文件: {check_points_definition_path}, 共 {len(check_points_definitions)} 个定义")
                        
                    except json.JSONDecodeError as je:
                        logger.error(f"检查点定义文件JSON解析失败: {je}, 位置: {je.pos}, 行: {je.lineno}")
                        problem_text = content[max(0, je.pos-30):min(len(content), je.pos+30)]
                        logger.error(f"JSON解析问题部分: {problem_text}")
                        
                        # 尝试更全面的修复和重新解析
                        try:
                            # 替换单引号为双引号
                            fixed_content = content.replace("'", '"')
                            # 移除注释
                            import re
                            fixed_content = re.sub(r'//.*?\n', '\n', fixed_content)
                            fixed_content = re.sub(r'/\*.*?\*/', '', fixed_content, flags=re.DOTALL)
                            
                            # 如果使用了缩写格式（如JS中的无引号键名），尝试修复
                            fixed_content = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', fixed_content)
                            
                            # 尝试解析
                            check_points_definitions = json.loads(fixed_content)
                            checking_expert.check_points_definitions = check_points_definitions
                            logger.info(f"成功修复并加载检查点定义，共 {len(check_points_definitions) if isinstance(check_points_definitions, list) else 1} 个定义")
                        except Exception as repair_err:
                            logger.error(f"尝试修复检查点定义失败: {repair_err}")
                else:
                    logger.warning("检查点定义文件为空")
            except Exception as e:
                logger.error(f"加载检查点定义文件时出错: {e}")
                import traceback
                logger.error(f"异常详情: {traceback.format_exc()}")
        else:
            logger.warning(f"检查点定义文件不存在: {check_points_definition_path}")
    
    # 组建团队
    team = Team()
    team.hire([parsing_expert, checking_expert, summary_expert])
    team.invest(investment)
    
    # 启动项目，将用户请求发送给文档解析专家
    team.run_project(document_content, send_to="DocumentParsingExpert")
    
    # 运行团队，每个检查点需要2轮交互（解析专家→检查专家→解析专家），加上最后的总结轮，所以n_round需要设置得足够大
    await team.run(n_round=n_round)
    
    logger.info(f"文档检查完成，详细报告已保存到 {output_file_path}")


def main(document_path: str, output_path: str = "document_check_report.md", check_points_path: Optional[str] = None, check_points_definition_path: Optional[str] = "check_points_definition.json", investment: float = 10.0, n_round: int = 15):
    """
    主函数，运行需求文档检查系统

    Args:
        document_path: 需求文档文件路径
        output_path: 检查报告输出文件路径
        check_points_path: 检查点文件路径，JSON格式，包含检查点列表
        check_points_definition_path: 检查点定义文件路径，包含详细的检查标准
        investment: 团队投资金额
        n_round: 最大运行轮数
    """
    try:
        # 检查文件路径
        if not os.path.exists(document_path):
            print(f"错误: 需求文档文件不存在: {document_path}")
            return
        
        # 读取文档内容
        try:
            with open(document_path, 'r', encoding='utf-8') as f:
                document_content = f.read()
                print(f"成功读取文档内容，大小: {len(document_content)} 字符")
        except UnicodeDecodeError:
            # 尝试不同的编码
            try:
                with open(document_path, 'r', encoding='gbk') as f:
                    document_content = f.read()
                    print(f"使用GBK编码成功读取文档内容，大小: {len(document_content)} 字符")
            except Exception as e:
                print(f"错误: 无法读取需求文档文件: {e}")
                return
        except Exception as e:
            print(f"错误: 无法读取需求文档文件: {e}")
            return
    
        # 读取检查点列表
        check_points = []
        if check_points_path:
            if os.path.exists(check_points_path):
                try:
                    with open(check_points_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print(f"成功读取检查点文件，大小: {len(content)} 字符")
                        
                        # 检查有效性
                        if content.strip():
                            # 预处理
                            if content.startswith('\ufeff'):
                                content = content[1:]
                                print("移除了检查点文件中的BOM标记")
                            
                            try:
                                check_points = json.loads(content)
                                if not isinstance(check_points, list):
                                    print(f"错误: 检查点文件格式错误，应该是列表，实际是 {type(check_points).__name__}")
                                    check_points = []
                                else:
                                    # 去除检查点列表中的重复项
                                    unique_check_points = []
                                    seen_check_points = set()
                                    
                                    for cp in check_points:
                                        if cp not in seen_check_points:
                                            unique_check_points.append(cp)
                                            seen_check_points.add(cp)
                                        else:
                                            print(f"警告: 检查点列表中存在重复项: {cp}")
                                    
                                    if len(unique_check_points) < len(check_points):
                                        print(f"去除了 {len(check_points) - len(unique_check_points)} 个重复检查点")
                                        check_points = unique_check_points
                                    
                                    print(f"成功解析检查点列表，共 {len(check_points)} 个检查点")
                            except json.JSONDecodeError as je:
                                print(f"错误: 检查点文件JSON解析失败: {je}")
                                # 尝试修复和重新解析
                                try:
                                    # 替换单引号为双引号
                                    fixed_content = content.replace("'", '"')
                                    # 尝试查找JSON的实际开始和结束
                                    json_start = fixed_content.find('[')
                                    if json_start != -1:
                                        fixed_content = fixed_content[json_start:]
                                    
                                    check_points = json.loads(fixed_content)
                                    
                                    # 去除检查点列表中的重复项
                                    unique_check_points = []
                                    seen_check_points = set()
                                    
                                    for cp in check_points:
                                        if cp not in seen_check_points:
                                            unique_check_points.append(cp)
                                            seen_check_points.add(cp)
                                    
                                    if len(unique_check_points) < len(check_points):
                                        print(f"去除了 {len(check_points) - len(unique_check_points)} 个重复检查点")
                                        check_points = unique_check_points
                                    
                                    print(f"成功修复并加载检查点列表，共 {len(check_points)} 个检查点")
                                except Exception as repair_err:
                                    print(f"尝试修复检查点列表失败: {repair_err}，将使用默认检查点")
                                    check_points = []
                        else:
                            print("检查点文件为空，将使用默认检查点")
                except Exception as e:
                    print(f"错误: 无法读取检查点文件: {e}")
                    check_points = []
            else:
                print(f"警告: 检查点文件不存在: {check_points_path}，将使用默认检查点")
                check_points = []
        
        # 如果没有检查点，使用默认值
        if not check_points:
            print("使用默认检查点列表")
            check_points = [
                "文档是否包含明确的功能需求",
                "文档是否包含非功能需求",
                "文档是否定义了关键术语",
                "文档是否包含约束条件",
                "文档结构是否清晰合理",
                "是否包含具体的截止时间",
                "是否定义了系统接口",
                "是否包含用户界面要求"
            ]
        else:
            print("使用自定义检查点列表：")
            for i, cp in enumerate(check_points):
                print(f"  {i+1}. {cp}")
        
        # 处理检查点定义路径
        if check_points_definition_path and not os.path.exists(check_points_definition_path):
            print(f"警告: 检查点定义文件不存在: {check_points_definition_path}")
            # 不阻止程序继续运行，只是给出警告
    
        if platform.system() == "Windows":
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
        # 计算适当的轮数，确保有足够的轮数完成所有检查点
        required_rounds = len(check_points) * 2 + 2  # 每个检查点需要2轮交互，加上初始解析和最终总结
        if n_round < required_rounds:
            print(f"警告: 提供的轮数 {n_round} 可能不足以完成所有 {len(check_points)} 个检查点的处理。推荐至少 {required_rounds} 轮。")
            if required_rounds <= 50:  # 设置一个合理的上限
                n_round = required_rounds
                print(f"自动调整轮数为: {n_round}")
            else:
                print(f"检查点数量过多，请考虑减少检查点或手动增加轮数")
        
        # 清除现有的输出文件（如果存在）
        if os.path.exists(output_path):
            try:
                os.remove(output_path)
                print(f"已清除现有的输出文件: {output_path}")
            except Exception as e:
                print(f"警告: 无法清除现有的输出文件: {e}")
        
        print(f"开始执行文档检查，共 {len(check_points)} 个检查点，最大轮数: {n_round}")
    
        # 运行文档检查
        asyncio.run(check_requirements_document(
            document_content=document_content, 
            check_points=check_points, 
            output_file_path=output_path, 
            investment=investment, 
            n_round=n_round,
            check_points_definition_path=check_points_definition_path
        ))
        
        print(f"文档检查完成，详细报告已保存到 {output_path}")
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        import traceback
        traceback_info = traceback.format_exc()
        print(traceback_info)
        
        # 保存错误信息到文件
        try:
            import datetime
            error_file = "document_checker_error.log"
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write(f"错误发生时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"错误信息: {str(e)}\n\n")
                f.write(f"详细堆栈: \n{traceback_info}")
            print(f"错误详情已保存到 {error_file}")
        except:
            print("保存错误日志时出错")


if __name__ == "__main__":
    fire.Fire(main)  # 运行方式：python documents_checker.py --document_path="requirements.md" --output_path="check_report.md" --check_points_path="check_points.json" --check_points_definition_path="check_points_definition.json" --investment=10.0 --n_round=15 