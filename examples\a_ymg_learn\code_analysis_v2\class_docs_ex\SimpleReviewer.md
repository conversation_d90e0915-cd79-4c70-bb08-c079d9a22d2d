# 类: SimpleReviewer

**文件路径**: d:\bigModel\curser\stock_test1\backend\MetaGPT\examples\a_ymg_learn\ex.py
**行号范围**: 125-132
**最后更新**: 2025-05-27 09:53:30
**继承**: Role

## 方法列表
### __init__
- **行号**: 129-132
- **参数**: self

## 方法调用关系
### SELF 调用
- SimpleReviewer.__init__ → SimpleReviewer.set_actions (行 131)
- SimpleReviewer.__init__ → SimpleReviewer._watch (行 132)
### STATIC 调用
- SimpleReviewer.__init__ → super (行 130)

## 源代码
```python
class SimpleReviewer(Role):
    name: str = "Charlie"
    profile: str = "SimpleReviewer"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.set_actions([SimpleWriteReview])
        self._watch([SimpleWriteTest])
```

## 智能分析
分析出错: <PERSON>rror communicating with <PERSON><PERSON>