#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2024/3/11
<AUTHOR> ale<PERSON><PERSON><PERSON>
@File    : structure_actions.py
"""
import ast
from typing import Dict, List
from datetime import datetime

from metagpt.actions import Action
from metagpt.logs import logger

from ..schema import CodeStructure

class AnalyzeStructureAction(Action):
    """分析代码结构"""
    name: str = "AnalyzeStructureAction"
    
    async def run(self, file_info: Dict) -> Dict:
        """分析代码结构"""
        try:
            file_path = file_info.get('file_path')
            content = file_info.get('content')
            
            if not file_path or not content:
                return {
                    'error': '无效的文件信息',
                    'analyzed_at': datetime.now().isoformat()
                }
            
            # 解析代码结构
            structure = await self._parse_structure(file_path, content)
            if not structure:
                return {
                    'error': f'解析文件 {file_path} 结构失败',
                    'analyzed_at': datetime.now().isoformat()
                }
            
            return {
                'structure': structure.dict(),
                'analyzed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            error_msg = f'分析结构时出错: {str(e)}'
            logger.error(error_msg)
            return {
                'error': error_msg,
                'analyzed_at': datetime.now().isoformat()
            }
            
    async def _parse_structure(self, file_path: str, content: str) -> CodeStructure:
        """解析代码结构"""
        try:
            # 解析代码
            tree = ast.parse(content)
            
            # 解析导入
            imports = []
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for name in node.names:
                        imports.append(name.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ''
                    for name in node.names:
                        imports.append(f"{module}.{name.name}")
                        
            # 解析类和函数
            classes = []
            functions = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        'name': node.name,
                        'start_line': node.lineno,
                        'end_line': node.end_lineno,
                        'methods': self._get_class_methods(node)
                    }
                    classes.append(class_info)
                    
                elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    if not isinstance(node.parent, ast.ClassDef):  # 只处理非方法的函数
                        func_info = {
                            'name': node.name,
                            'start_line': node.lineno,
                            'end_line': node.end_lineno,
                            'is_async': isinstance(node, ast.AsyncFunctionDef)
                        }
                        functions.append(func_info)
                        
            return CodeStructure(
                imports=imports,
                classes=classes,
                functions=functions,
                source_code=content,
                file_info={'file_path': file_path}
            )
            
        except Exception as e:
            logger.error(f"解析文件 {file_path} 结构出错: {str(e)}")
            return None
            
    def _get_class_methods(self, class_node: ast.ClassDef) -> List[Dict]:
        """获取类的方法信息"""
        methods = []
        for node in ast.iter_child_nodes(class_node):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                method_info = {
                    'name': node.name,
                    'start_line': node.lineno,
                    'end_line': node.end_lineno,
                    'is_async': isinstance(node, ast.AsyncFunctionDef)
                }
                methods.append(method_info)
        return methods 