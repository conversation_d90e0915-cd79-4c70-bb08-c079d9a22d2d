#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2024/3/11
<AUTHOR> ale<PERSON><PERSON><PERSON>
@File    : structure_analyzer.py
"""
import ast
from typing import Dict, List

from metagpt.schema import Message
from metagpt.logs import logger

from .base_analyzer import BaseAnalyzer
from ..schema import AnalysisMessage, CodeStructure
from ..actions.structure_actions import AnalyzeStructureAction

class StructureAnalyzer(BaseAnalyzer):
    """结构分析器"""
    name: str = "StructureAnalyzer"
    description: str = "负责分析代码结构"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.set_actions([AnalyzeStructureAction])
    
    async def _process(self, msg: Message) -> Message:
        """处理结构分析请求"""
        try:
            # 获取文件列表
            content = msg.content
            if isinstance(content, str):
                import json
                content = json.loads(content)
                
            files = content.get('files', [])
            if not files:
                raise ValueError("没有找到可分析的文件")
                
            # 分析所有文件的结构
            structures = {}
            action = AnalyzeStructureAction()
            
            for file_info in files:
                result = await action.run(file_info)
                if 'error' not in result and 'structure' in result:
                    file_path = file_info.get('file_path')
                    structures[file_path] = result['structure']
                    
            # 更新状态
            result = {
                'structures': structures,
                'total_files': len(structures)
            }
            self.update_state("completed", result=result)
            
            # 发送结果给函数分析器
            return AnalysisMessage(
                role=self.name,
                content=result,
                phase="structure_analysis_completed",
                processor={"FunctionAnalyzer"},
                dependencies={"FileAnalyzer"}
            )
            
        except Exception as e:
            error_msg = f"结构分析失败: {str(e)}"
            logger.error(error_msg)
            self.update_state("error", error=error_msg)
            return AnalysisMessage(
                role=self.name,
                content={"error": error_msg},
                phase="error",
                processor={self.name}
            )
            
    async def _parse_structure(self, file_path: str, content: str) -> CodeStructure:
        """解析代码结构"""
        try:
            # 解析代码
            tree = ast.parse(content)
            
            # 解析导入
            imports = []
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for name in node.names:
                        imports.append(name.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ''
                    for name in node.names:
                        imports.append(f"{module}.{name.name}")
                        
            # 解析类和函数
            classes = []
            functions = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        'name': node.name,
                        'start_line': node.lineno,
                        'end_line': node.end_lineno,
                        'methods': self._get_class_methods(node)
                    }
                    classes.append(class_info)
                    
                elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    if not isinstance(node.parent, ast.ClassDef):  # 只处理非方法的函数
                        func_info = {
                            'name': node.name,
                            'start_line': node.lineno,
                            'end_line': node.end_lineno,
                            'is_async': isinstance(node, ast.AsyncFunctionDef)
                        }
                        functions.append(func_info)
                        
            return CodeStructure(
                imports=imports,
                classes=classes,
                functions=functions,
                source_code=content,
                file_info={'file_path': file_path}
            )
            
        except Exception as e:
            logger.error(f"解析文件 {file_path} 结构出错: {str(e)}")
            return None
            
    def _get_class_methods(self, class_node: ast.ClassDef) -> List[Dict]:
        """获取类的方法信息"""
        methods = []
        for node in ast.iter_child_nodes(class_node):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                method_info = {
                    'name': node.name,
                    'start_line': node.lineno,
                    'end_line': node.end_lineno,
                    'is_async': isinstance(node, ast.AsyncFunctionDef)
                }
                methods.append(method_info)
        return methods 