@startuml 文档检查系统类图

' 类定义
abstract class Action {
  - name: str
  - PROMPT_TEMPLATE: str
  + run(): Any
}

abstract class Role {
  - name: str
  - profile: str
  - actions: List[Action]
  - memories: List[Message]
  + _act(): Message
  + _observe(): int
}

class Team {
  - roles: List[Role]
  - investment: float
  + hire(roles: List[Role]): None
  + invest(amount: float): None
  + run_project(idea: Any, send_to: str): None
  + run(n_round: int): None
}

class Message {
  - content: Any
  - role: str
  - cause_by: Type
  - sent_from: str
  - send_to: Set[str]
}

class UserRequirement {
  - content: Any
}

' Action类的具体实现
class ParseDocument {
  - name: str
  - PROMPT_TEMPLATE: str
  + run(document_content: str): str
}

class CheckDocument {
  - name: str
  - PROMPT_TEMPLATE: str
  + run(parsed_document: str, check_point: str): str
}

class SummarizeDocument {
  - name: str
  - PROMPT_TEMPLATE: str
  + run(check_results: List[str]): str
}

' Role类的具体实现
class DocumentParsingExpert {
  - name: str
  - profile: str
  - next_role: str
  + _act(): Message
}

class DocumentCheckingExpert {
  - name: str
  - profile: str
  - next_role: str
  + _observe(): int
  + _act(): Message
}

class DocumentSummaryExpert {
  - name: str
  - profile: str
  + _observe(): int
  + _act(): Message
}

' 继承关系
Action <|-- ParseDocument
Action <|-- CheckDocument
Action <|-- SummarizeDocument

Role <|-- DocumentParsingExpert
Role <|-- DocumentCheckingExpert
Role <|-- DocumentSummaryExpert

' 关联关系
DocumentParsingExpert o-- ParseDocument
DocumentCheckingExpert o-- CheckDocument
DocumentSummaryExpert o-- SummarizeDocument

Team o-- Role
Role o-- Action
Role o-- Message

DocumentParsingExpert ..> UserRequirement: watches
DocumentCheckingExpert ..> ParseDocument: watches
DocumentSummaryExpert ..> CheckDocument: watches

@enduml

@startuml 文档检查系统流程图

actor 用户
participant "文档解析专家" as Parser
participant "文档检查专家" as Checker
participant "文档总结专家" as Summarizer

' 初始化阶段
用户 -> Parser: 提供文档内容和检查点列表
note right: 文档内容和检查点以JSON形式传递

' 解析阶段
Parser -> Parser: 解析文档
Parser -> Checker: 传递解析结果和检查点列表
note right: 通过Message对象传递
          包含parsed_document和check_points字段

' 检查阶段
Checker -> Checker: 遍历检查点列表
loop 对每个检查点
    Checker -> Checker: 检查文档是否满足该检查点
end
Checker -> Summarizer: 传递所有检查结果
note right: 通过Message对象传递
          包含check_results字段列表

' 总结阶段
Summarizer -> Summarizer: 汇总检查结果，生成报告
Summarizer -> 用户: 返回最终检查报告
note right: 包含总体评估、满足/不满足/部分满足的检查点、改进建议

@enduml 