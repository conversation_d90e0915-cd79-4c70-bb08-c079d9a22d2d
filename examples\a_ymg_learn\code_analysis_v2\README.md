# 代码分析工具 V2.0

这是一个强大的代码分析工具，可以帮助你分析Python项目的结构、函数和依赖关系,不依赖于循环分析。

## 功能特点

- 文件结构分析
- 代码结构分析
- 函数和方法分析
- 依赖关系分析
- 生成详细的Markdown格式报告

## 使用方法

### 基本用法

1. 使用Python直接执行：
```bash
python code_analyzer_v2.py [target_path] [--investment INVESTMENT] [--output_file OUTPUT_FILE]
```

2. 使用Python模块方式执行（推荐）：
```bash
python -m code_analyzer_v2 [target_path] [--investment INVESTMENT] [--output_file OUTPUT_FILE]
```

### 参数说明

- `target_path`：要分析的目标路径（默认为当前目录 "."）
- `--investment`：投资额度（默认为 3.0）
- `--output_file`：输出文件名（默认为 "code_analysis_report.md"）

### 使用示例

1. 分析当前目录：
```bash
python -m code_analyzer_v2
```

2. 分析指定目录：
```bash
python -m code_analyzer_v2 /path/to/your/project
```

3. 自定义投资额度和输出文件：
```bash
python -m code_analyzer_v2 /path/to/your/project --investment 5.0 --output_file my_analysis.md

python -m examples.a_ymg_learn.code_analysis_v2.code_analyzer_v2 d:\bigModel\curser\stock_test1\backend\MetaGPT\examples\a_ymg_learn\ex.py --investment 5.0 --output_file d:\bigModel\curser\stock_test1\backend\MetaGPT\examples\a_ymg_learn\code_analysis_v2\my_analysis.md
```

## 输出说明

工具会生成一个Markdown格式的分析报告，包含以下内容：

1. 文件分析
   - 文件总数
   - 文件列表
   - 目录结构

2. 代码结构分析
   - 类结构图
   - 函数列表
   - 导入依赖图

3. 函数分析
   - 功能说明
   - 参数分析
   - 返回值说明
   - 实现逻辑
   - 代码质量评估

## 注意事项

- 确保目标路径存在且有读取权限
- 建议在分析大型项目时适当增加投资额度
- 输出文件会自动创建或覆盖已存在的文件

## 依赖要求

- Python 3.7+
- 相关依赖包（详见requirements.txt） 