#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试检查点去重逻辑
"""

import json

def test_deduplication():
    """测试检查点去重逻辑"""
    content = '["是否包含具体的截止时间", "是否包含具体的截止时间", "文档是否包含明确的功能需求"]'
    check_points = json.loads(content)
    print(f"原始列表: {check_points}")
    
    # 去重逻辑
    unique_check_points = []
    seen_check_points = set()
    
    for cp in check_points:
        print(f"处理: {cp}")
        if cp not in seen_check_points:
            unique_check_points.append(cp)
            seen_check_points.add(cp)
            print(f"  添加到唯一列表")
        else:
            print(f"  跳过重复项")
    
    print(f"去重后列表: {unique_check_points}")
    print(f"原始长度: {len(check_points)}, 去重后长度: {len(unique_check_points)}")

if __name__ == "__main__":
    test_deduplication() 