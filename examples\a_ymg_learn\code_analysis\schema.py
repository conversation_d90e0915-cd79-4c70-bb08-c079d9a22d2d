#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码分析数据结构
作者: Assistant
创建时间: 2024-03-11
更新时间: 2024-03-14
"""

from typing import Any, Optional, Set, Dict, List
from datetime import datetime
from pydantic import BaseModel, Field, field_validator

from metagpt.schema import Message
from metagpt.utils.common import any_to_str_set

class FileInfo(BaseModel):
    """文件信息"""
    file_path: str
    content: str = ""
    size: int = 0
    last_modified: datetime = Field(default_factory=datetime.now)

class CodeStructure(BaseModel):
    """代码结构"""
    file_path: str
    classes: List[Dict] = Field(default_factory=list)
    functions: List[Dict] = Field(default_factory=list)
    imports: List[str] = Field(default_factory=list)
    
class AnalysisReport(BaseModel):
    """分析报告"""
    content: str
    generated_at: datetime = Field(default_factory=datetime.now)
    metadata: Dict = Field(default_factory=dict)

class AnalysisState(BaseModel):
    """分析状态"""
    phase: str = "init"
    files_analyzed: int = 0
    structures_analyzed: int = 0
    functions_analyzed: int = 0
    report_generated: bool = False
    error: Optional[str] = None
    last_updated: datetime = Field(default_factory=datetime.now)

class AnalysisMessage(Message):
    """代码分析消息"""
    phase: str = Field(default="init")
    processor: Set[str] = Field(default=set(), validate_default=True)
    dependencies: Set[str] = Field(default=set(), validate_default=True)
    
    @field_validator("processor", "dependencies", mode="before")
    @classmethod
    def validate_sets(cls, v: Any) -> Set[str]:
        return any_to_str_set(v if v else set()) 