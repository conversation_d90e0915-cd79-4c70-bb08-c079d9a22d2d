# 需求文档检查系统开发日志

## 系统概述

需求文档检查系统是基于MetaGPT框架开发的一个智能文档分析工具，通过三个专家角色的协作，对需求文档进行解析、检查和总结，生成详细的检查报告。

系统架构由三个专家角色组成：
1. 文档解析专家（DocumentParsingExpert）：负责解析用户提供的需求文档，提取文档结构、功能需求、非功能需求等内容
2. 文档检查专家（DocumentCheckingExpert）：基于特定检查点对解析后的文档进行评估
3. 文档总结专家（DocumentSummaryExpert）：汇总所有检查结果，生成最终报告

## 功能特性

- 支持自定义检查点，可通过JSON文件配置
- 检查结果实时保存到指定文件
- 详细的检查报告包含每个检查点的评估结果
- 综合总结报告提供整体评估和改进建议

## 更新日志

### 2024-XX-XX 最新更新

1. 新增功能：动态提示词模板
   - 解析系统现在支持从check_points_definition.json文件中动态构建提示词模板
   - 提示词模板根据检查点列表自动调整，只提取相关字段，避免额外信息干扰
   - 每个检查点ID映射到特定的解析字段，使解析更加精准和高效
   - 减少了代码中的硬编码提示词，提高了系统的灵活性和可维护性

2. 优化功能：检查点与定义文件集成
   - DocumentParsingExpert现在可以直接从check_points_definition.json文件中读取检查点列表
   - 无需额外的check_points.json文件，简化了配置流程
   - 系统会自动查找可能的检查点定义文件路径，增强了配置的灵活性
   - 新增详细日志输出，帮助追踪检查点加载和映射过程

### 2024-XX-XX 更新

1. 新增功能：将检查结果保存到指定文档
   - DocumentCheckingExpert现在会将每个检查点的评估结果实时保存到指定的文档文件中
   - 每个检查点结果按顺序记录，包含检查点描述和详细评估
   
2. 新增功能：总结报告添加到文档末尾
   - DocumentSummaryExpert会将综合评估报告添加到检查结果文档的末尾
   - 总结报告包含总体评估、满足/不满足/部分满足的检查点列表以及改进建议
   
3. 优化功能：支持自定义输出路径
   - 通过命令行参数`--output_path`指定检查报告的输出文件路径
   - 默认输出文件为`document_check_report.md`

4. 新增功能：标准化检查点定义
   - 新增`check_points_definition.json`文件，为每个检查点提供标准化定义
   - 每个检查点定义包含详细说明、评判标准和示例，大幅提高检查准确度
   - 检查专家在评估时会参考标准定义，减少歧义和误判情况
   - 特别改进了对日期、截止时间等时间信息的准确识别能力

5. 增强功能：文档解析能力提升
   - 改进了ParseDocument的提示模板，提取更多文档要素
   - 对截止时间、系统接口、用户界面等关键信息进行特别关注
   - 解析结果更加结构化和准确，支持检查专家做出更精确的评估

6. 优化功能：精简检查点配置
   - 现在系统会优先从`check_points_definition.json`文件中提取检查点列表，减少重复配置
   - 修复了检查点定义格式解析的问题，正确处理嵌套的criteria和examples字段
   - 增强了检查点定义与检查点之间的匹配算法，支持部分匹配和ID匹配

7. 优化功能：增强检查点评估
   - 改进了CheckDocumentPoint的提示模板，更明确地引导AI使用检查点定义
   - 优化了检查结果的格式，确保评估结果的一致性
   - 增加了对评估过程的错误处理和重试机制，提高系统稳定性

## 使用方法

### 命令行参数

- `document_path`: 需求文档文件路径（必需）
- `output_path`: 检查报告输出文件路径（可选，默认为 "document_check_report.md"）
- `check_points_path`: 检查点文件路径，JSON格式，包含检查点列表（可选，优先级低于check_points_definition_path）
- `check_points_definition_path`: 检查点定义文件路径，包含详细的检查标准（可选，默认为 "check_points_definition.json"）
- `investment`: 团队投资金额（可选，默认为 10.0）
- `n_round`: 最大运行轮数（可选，默认为 15）

### 配置文件

系统使用两种配置文件：

1. **检查点定义文件**（默认：`check_points_definition.json`）：
   - 包含检查点的详细定义，包括ID、名称、描述、判断标准和示例
   - 系统会优先从该文件提取检查点列表（使用其中的"name"字段）
   - 格式示例：
     ```json
     [
         {
             "id": "functionality_requirements",
             "name": "文档是否包含明确的功能需求",
             "description": "检查文档是否包含清晰、具体的功能需求描述",
             "criteria": {
                 "satisfied": "文档详细列出了所有功能需求，包括具体的实现方法和预期结果",
                 "partially_satisfied": "文档提到了一些功能需求，但描述不够详细或不够具体",
                 "not_satisfied": "文档完全没有提到功能需求或提到的信息过于模糊"
             },
             "examples": {
                 "satisfied": "需要开发用户登录功能，支持账号密码和短信验证码两种登录方式",
                 "partially_satisfied": "系统需要支持用户登录功能",
                 "not_satisfied": "文档中没有任何关于功能的描述"
             }
         }
     ]
     ```

2. **检查点列表文件**（可选）：
   - 只有在无法从检查点定义文件中提取检查点列表时才会使用
   - 仅包含简单的检查点字符串列表
   - 格式示例：
     ```json
     [
       "文档是否包含明确的功能需求",
       "是否定义了系统接口",
       "是否包含具体的截止时间",
       "是否包含开发人员信息"
     ]
     ```

**注意**：如果两个文件都不提供，或者无法从中提取检查点，系统将使用默认检查点列表。

## 检查报告格式

生成的检查报告包含两部分：

1. 检查点详细结果
   每个检查点的评估结果，包含：
   - 检查点内容
   - 评估结果（满足/不满足/部分满足）
   - 详细说明
   - 改进建议

2. 总结报告
   对所有检查结果的汇总分析，包含：
   - 总体评估概述
   - 已满足的检查点列表
   - 未满足的检查点列表
   - 部分满足的检查点列表
   - 具体的改进建议

## 实际应用示例

假设我们有一个电商系统的需求文档，想检查它是否完整合理，可以执行：

```bash
python document_checker.py --document_path='req.md' --check_points_definition_path="check_points_definition.json" --disable_hallucination_detection=True ----disable_filtering=True --investment=3.0 --n_round=1
```

系统会解析文档内容，检查是否包含清晰的功能描述、非功能需求等，并生成详细的评估报告，帮助我们发现文档中的不足之处，提供改进建议。

## 文件说明

- `documents_checker.py`: 主程序文件，包含系统的核心代码
- `documents_checker_design.md`: 系统设计文档，详细说明系统的架构和组件
- `uml_diagrams.puml`: 系统UML图，包含类图和流程图
- `example_document.md`: 示例需求文档，用于测试系统
- `check_points.json`: 默认检查点列表，可根据需要自定义

## 自定义和扩展

可以根据需要修改以下部分来自定义和扩展系统：

1. 修改 `ParseDocument` 类的 `PROMPT_TEMPLATE` 来自定义文档解析规则
2. 修改 `CheckDocument` 类的 `PROMPT_TEMPLATE` 来自定义检查规则
3. 修改 `SummarizeDocument` 类的 `PROMPT_TEMPLATE` 来自定义报告格式
4. 添加新的 `Action` 和 `Role` 来扩展系统功能

## 依赖项

- Python 3.8+
- MetaGPT
- Fire