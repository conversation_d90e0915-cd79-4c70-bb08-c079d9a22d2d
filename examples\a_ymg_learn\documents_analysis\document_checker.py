"""
文件名: MetaGPT/examples/a_ymg_learn/documents_analysis/document_checker.py
创建日期: 2024年
描述: 实现基于MetaGPT的需求文档检查系统入口
"""

import asyncio
import platform
import os
import sys
import traceback
import re
from typing import List, Optional, Dict, Tuple

import fire
from metagpt.logs import logger
from metagpt.team import Team

# 添加当前目录到路径以便导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入模块
from modules.actions import ParseDocument, CheckDocumentPoint, SummarizeDocument, VerifyLogicalConsistency
from modules.roles import DocumentParsingExpert, DocumentCheckingExpert, DocumentSummaryExpert, HallucinationDetector
from modules.utils import (
    read_document_file, 
    read_json_file, 
    clean_check_points_list, 
    get_default_check_points,
    calculate_required_rounds,
    prepare_output_file
)

async def check_requirements_document(
    filepath: str,
    env_file: Optional[str] = "",
    output_file: Optional[str] = "output.md",
    local_resources_dir: Optional[str] = "",
    model: Optional[str] = None,
    re_examine_pass_check_points: bool = False,
    disable_hallucination_detection: bool = False,
    investment: float = 10.0,
    n_round: int = 15,
    check_points_definition_path: Optional[str] = "check_points_definition.json",
) -> None:
    """
    检查需求文档是否满足指定的标准

    Args:
        filepath: 文档路径
        env_file: 环境变量文件路径
        output_file: 输出文件路径
        local_resources_dir: 本地资源目录路径，用于替代从网络下载资源
        model: LLM模型名称
        re_examine_pass_check_points: 是否重新检查通过的检查点
        disable_hallucination_detection: 是否禁用幻觉检测验证功能（但幻觉检测器始终会被添加到团队）
        investment: 团队投资金额
        n_round: 最大运行轮数
        check_points_definition_path: 检查点定义文件路径
    """
    if env_file:
        # 加载环境变量
        from dotenv import load_dotenv
        load_dotenv(dotenv_path=env_file)

    try:
        # 检查文件是否存在
        if not os.path.exists(filepath):
            logger.error(f"文件 {filepath} 不存在")
            raise FileNotFoundError(f"文件 {filepath} 不存在")

        # 读取文档内容
        with open(filepath, "r", encoding="utf-8") as f:
            document_content = f.read()
            
        # 获取检查点列表
        check_points = get_default_check_points()
        total_check_points = len(check_points)
        
        # 计算所需轮次
        rounds_needed = calculate_required_rounds(total_check_points)
        logger.info(f"总检查点数: {total_check_points}, 所需轮次: {rounds_needed}")
        
        # 初始化角色
        document_parser = DocumentParsingExpert(check_points=check_points)
        
        document_checker = DocumentCheckingExpert(
            disable_hallucination_detection=disable_hallucination_detection,
            re_examine_pass_check_points=re_examine_pass_check_points,
            output_file_path=output_file,
        )
        
        document_summary_expert = DocumentSummaryExpert(output_file_path=output_file)
        
        # 创建团队角色列表 - 无论参数如何设置，始终包含所有角色
        team_roles = [
            document_parser, 
            document_checker, 
            HallucinationDetector(disable_hallucination_detection=disable_hallucination_detection),
            document_summary_expert
        ]
        
        logger.info("幻觉检测器已添加到团队，将对每个检查点进行幻觉检查")
        if disable_hallucination_detection:
            logger.info("幻觉检测功能已禁用，但幻觉检测器仍会处理每个检查点并记录日志")
        
        # 仅当提供了检查点定义文件路径时加载，并且严格按照check_points中的检查点进行检查
        if check_points_definition_path and os.path.exists(check_points_definition_path):
            try:
                # 读取和解析检查点定义文件
                check_points_definitions, success = read_json_file(check_points_definition_path)
                
                if success and check_points_definitions:
                    # 验证检查点定义的格式
                    if isinstance(check_points_definitions, dict):
                        # 如果是字典，将其转换为列表
                        logger.info("检查点定义是字典格式，转换为列表")
                        check_points_definitions = [check_points_definitions]
                    elif not isinstance(check_points_definitions, list):
                        logger.error(f"检查点定义格式错误：应该是列表或字典，实际是 {type(check_points_definitions).__name__}")
                        check_points_definitions = []
                    
                    # 过滤定义，只保留check_points中包含的检查点
                    filtered_definitions = []
                    for definition in check_points_definitions:
                        if definition.get("name") in check_points:
                            filtered_definitions.append(definition)
                            logger.info(f"保留定义：{definition.get('name')}")
                        else:
                            logger.info(f"跳过未使用的定义：{definition.get('name')}")
                    
                    # 将过滤后的检查点定义传递给检查专家
                    document_checker.check_points_definitions = filtered_definitions
                    logger.info(f"已加载检查点定义文件: {check_points_definition_path}, 共 {len(filtered_definitions)} 个定义")
                else:
                    logger.warning(f"无法加载或解析检查点定义文件: {check_points_definition_path}")
                
            except Exception as e:
                logger.error(f"加载检查点定义文件时出错: {e}")
                logger.error(traceback.format_exc())
        else:
            if check_points_definition_path:
                logger.warning(f"检查点定义文件不存在: {check_points_definition_path}")
            else:
                logger.warning("未提供检查点定义文件路径")
        
        # 组建团队
        team = Team()
        team.hire(team_roles)
        team.invest(investment)
        
        # 启动项目，将用户请求发送给文档解析专家
        team.run_project(document_content, send_to="DocumentParsingExpert")
        
        # 运行团队
        await team.run(n_round=n_round)
        
        logger.info(f"文档检查完成，详细报告已保存到 {output_file}")
    except Exception as e:
        logger.error(f"执行过程中发生错误: {e}")
        traceback_info = traceback.format_exc()
        logger.error(traceback_info)
        
        # 保存错误信息到文件
        try:
            import datetime
            error_file = "document_checker_error.log"
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write(f"错误发生时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"错误信息: {str(e)}\n\n")
                f.write(f"详细堆栈: \n{traceback_info}")
            logger.info(f"错误详情已保存到 {error_file}")
        except:
            logger.info("保存错误日志时出错")

def filter_irrelevant_check_points(check_points: List[str], document_content: str) -> List[str]:
    """过滤掉与文档内容明显不相关的检查点
    
    Args:
        check_points: 检查点列表
        document_content: 文档内容
        
    Returns:
        List[str]: 过滤后的检查点列表
    """
    filtered_check_points = []
    skipped_check_points = []
    
    # 定义更精确的检查类别和关键词关联
    irrelevant_categories = {
        "开发人员": {
            "keywords": ["团队", "人员", "开发者", "工程师", "联系方式", "姓名", "职责", "开发团队"],
            "check_patterns": ["开发人员", "开发团队", "团队", "联系人", "人员配置"]
        },
        "预算": {
            "keywords": ["成本", "费用", "金额", "开销", "资金", "预算", "投资", "百万", "万元"],
            "check_patterns": ["预算", "成本", "费用", "资金", "投入"]
        },
        "接口": {
            "keywords": ["API", "接口", "调用", "服务", "端口", "协议", "RESTful"],
            "check_patterns": ["接口", "API", "系统接口"]
        },
        "UI": {
            "keywords": ["界面", "UI", "用户体验", "交互", "前端", "设计", "样式", "布局"],
            "check_patterns": ["界面", "UI", "用户界面", "交互"]
        }
    }
    
    # 检测文档类型
    doc_type = detect_document_type(document_content)
    logger.info(f"检测到文档类型: {doc_type}")
    
    # 文档内容预处理 - 转小写便于匹配
    lower_content = document_content.lower()
    
    # 对于短文档，某些检查点可能不适用
    doc_length = len(document_content)
    is_short_doc = doc_length < 800
    logger.info(f"文档长度: {doc_length} 字符, {'短文档' if is_short_doc else '标准长度文档'}")
    
    # 首先检查文档中是否存在每个类别的关键词
    category_presence = {}
    for category, data in irrelevant_categories.items():
        # 检查该类别的关键词是否在文档中出现
        keywords_found = [kw for kw in data["keywords"] if kw in lower_content]
        category_presence[category] = len(keywords_found) > 0
        if keywords_found:
            logger.info(f"在文档中找到 {category} 相关的关键词: {', '.join(keywords_found)}")
        else:
            logger.info(f"文档中没有找到任何 {category} 相关的关键词")
    
    # 内容验证函数 - 更精确地检查文档是否包含特定内容
    def verify_content_exists(category: str, content: str) -> Tuple[bool, str]:
        """验证文档中是否真正包含特定类别的实质内容"""
        category_data = irrelevant_categories.get(category, {})
        keywords = category_data.get("keywords", [])
        
        # 不仅检查关键词存在，还检查上下文是否真正描述了该类别的内容
        for kw in keywords:
            if kw in lower_content:
                # 提取关键词所在的上下文（前后30个字符）
                pattern = f".{{0,30}}{kw}.{{0,30}}"
                contexts = re.findall(pattern, lower_content)
                if contexts:
                    return True, contexts[0]
        
        return True, ""
    
    for cp in check_points:
        cp_lower = cp.lower()
        skip_reasons = []
        
        # 遍历每个内容类别检查
        for category, data in irrelevant_categories.items():
            # 如果检查点与该类别相关
            if any(pattern in cp_lower for pattern in data["check_patterns"]):
                # 验证文档是否包含该类别的实质内容
                has_content, context = verify_content_exists(category, lower_content)
                
                if not has_content:
                    # 对于特定类型的文档，某些检查点总是不相关的
                    if is_short_doc or doc_type in ["api", "design"] and category in ["预算", "开发人员"]:
                        skip_reasons.append(f"文档不包含任何{category}相关内容")
                    elif category in ["预算", "开发人员"] and not category_presence[category]:
                        # 对于预算和开发人员，即使是长文档也应严格过滤
                        skip_reasons.append(f"文档不包含任何{category}相关内容")
        
        if skip_reasons:
            # 记录被跳过的检查点和原因
            skipped_check_points.append((cp, " 且 ".join(skip_reasons)))
        else:
            filtered_check_points.append(cp)
    
    # 记录过滤结果
    if skipped_check_points:
        logger.info(f"已过滤 {len(skipped_check_points)} 个与文档不相关的检查点:")
        for cp, reason in skipped_check_points:
            logger.info(f"  - '{cp}' (原因: {reason})")
    
    return filtered_check_points

def detect_document_type(document_content: str) -> str:
    """检测文档类型
    
    Args:
        document_content: 文档内容
        
    Returns:
        str: 文档类型
    """
    # 文档类型关键词 - 增强版本
    doc_type_keywords = {
        "requirements": ["需求", "要求", "功能", "特性", "实现", "系统应", "应包含", "应支持"],
        "api": ["接口", "API", "调用", "请求", "响应", "服务", "端点", "参数", "返回值", "HTTP"],
        "design": ["设计", "架构", "结构", "模型", "流程", "类图", "组件", "模块"],
        "plan": ["计划", "进度", "时间表", "里程碑", "排期", "阶段", "交付", "期限"]
    }
    
    # 计算文档内容中各类型关键词的出现频率
    lower_content = document_content.lower()
    type_scores = {}
    type_keywords_found = {}
    
    for t_name, keywords in doc_type_keywords.items():
        keywords_found = [kw for kw in keywords if kw in lower_content]
        score = len(keywords_found)
        type_scores[t_name] = score
        type_keywords_found[t_name] = keywords_found
    
    # 如果没有足够信息判断，默认为需求文档
    if not type_scores or max(type_scores.values()) == 0:
        logger.info("无法确定文档类型，默认为需求文档")
        return "requirements"
    
    # 返回得分最高的文档类型
    best_type = max(type_scores, key=type_scores.get)
    logger.info(f"文档类型判定为 {best_type}，找到相关关键词: {', '.join(type_keywords_found[best_type])}")
    return best_type

def main(
    document_path: str, 
    output_path: str = "document_check_report.md", 
    check_points_path: Optional[str] = None, 
    check_points_definition_path: Optional[str] = "check_points_definition.json", 
    investment: float = 10.0, 
    n_round: int = 15,
    disable_filtering: bool = False,
    disable_hallucination_detection: bool = False
):
    """
    主函数，运行需求文档检查系统

    Args:
        document_path: 需求文档文件路径
        output_path: 检查报告输出文件路径
        check_points_path: 检查点文件路径，JSON格式，包含检查点列表（可选，优先级低于check_points_definition_path）
        check_points_definition_path: 检查点定义文件路径，包含详细的检查标准，将从中提取检查点列表
        investment: 团队投资金额
        n_round: 最大运行轮数
        disable_filtering: 是否禁用检查点过滤
        disable_hallucination_detection: 是否禁用幻觉检测，设为True时幻觉检测器只会记录日志但不会验证结果
    """
    try:
        # 读取文档内容
        document_content, doc_success = read_document_file(document_path)
        if not doc_success:
            print(f"错误: 无法读取需求文档文件: {document_path}")
            return
    
        # 检查点列表变量初始化
        check_points = []
    
        # 首先尝试从定义文件中读取检查点
        if check_points_definition_path and os.path.exists(check_points_definition_path):
            json_data, success = read_json_file(check_points_definition_path)
            if success and json_data and isinstance(json_data, list):
                # 从检查点定义中提取检查点名称
                check_points = [item.get("name", "") for item in json_data if item.get("name")]
                if check_points:
                    check_points = clean_check_points_list(check_points)
                    print(f"成功从检查点定义文件提取出 {len(check_points)} 个检查点")
                else:
                    print(f"警告: 检查点定义文件无法提取有效的检查点名称")
                    # 如果无法提取有效的检查点名称，使用默认的检查点列表
                    # 注意：默认检查点列表已修改为仅包含check_points_definition.json中定义的检查点
                    check_points = get_default_check_points()
                    print(f"使用默认检查点列表，共 {len(check_points)} 个检查点")
            else:
                print(f"警告: 检查点定义文件格式错误或无法读取")
                # 使用默认检查点列表
                check_points = get_default_check_points()
                print(f"使用默认检查点列表，共 {len(check_points)} 个检查点")
        elif check_points_definition_path:
            print(f"警告: 检查点定义文件不存在: {check_points_definition_path}")
            # 使用默认检查点列表
            check_points = get_default_check_points()
            print(f"使用默认检查点列表，共 {len(check_points)} 个检查点")
        else:
            # 如果没有指定check_points_definition_path
            # 使用默认检查点列表
            check_points = get_default_check_points()
            print(f"未指定检查点定义文件，使用默认检查点列表，共 {len(check_points)} 个检查点")
        
        # 如果从定义文件中未获取到检查点，但用户明确提供了check_points_path，则尝试读取
        # 注意：这种情况现在只作为备选方案，优先使用check_points_definition.json
        if not check_points and check_points_path:
            json_data, success = read_json_file(check_points_path)
            if success and json_data:
                if isinstance(json_data, list):
                    check_points = json_data
                    check_points = clean_check_points_list(check_points)
                    print(f"成功加载检查点列表，共 {len(check_points)} 个检查点")
                else:
                    print(f"错误: 检查点文件格式错误，应该是列表，实际是 {type(json_data).__name__}")
        
        # 确保check_points不为空
        if not check_points:
            check_points = get_default_check_points()
            print(f"无法获取有效的检查点列表，使用默认检查点列表，共 {len(check_points)} 个检查点")
        
        # 过滤不相关的检查点
        original_count = len(check_points)
        if not disable_filtering:
            check_points = filter_irrelevant_check_points(check_points, document_content)
            if len(check_points) < original_count:
                print(f"已过滤 {original_count - len(check_points)} 个与文档不相关的检查点")
        else:
            print("已禁用检查点过滤，将使用所有定义的检查点")
        
        print("使用检查点列表：")
        for i, cp in enumerate(check_points):
            print(f"  {i+1}. {cp}")
        
        if platform.system() == "Windows":
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
        # 计算适当的轮数，确保有足够的轮数完成所有检查点
        required_rounds = calculate_required_rounds(check_points)
        if n_round < required_rounds:
            print(f"警告: 提供的轮数 {n_round} 可能不足以完成所有 {len(check_points)} 个检查点的处理。推荐至少 {required_rounds} 轮。")
            n_round = required_rounds
            print(f"自动调整轮数为: {n_round}")
        
        # 准备输出文件
        prepare_output_file(output_path)
        
        print(f"开始执行文档检查，共 {len(check_points)} 个检查点，最大轮数: {n_round}")
        if disable_filtering:
            print("检查点过滤已禁用，将使用所有定义的检查点")
        
        # 幻觉检测器信息
        print("系统将对每个检查点结果进行幻觉检查")
        
        if disable_hallucination_detection:
            print("幻觉检测验证功能已禁用，幻觉检测器将只记录日志不执行验证")
        else:
            print("幻觉检测验证功能已启用，系统将验证评估结果与原始文档的逻辑一致性")
    
        # 运行文档检查
        asyncio.run(check_requirements_document(
            filepath=document_path,
            output_file=output_path,
            check_points_definition_path=check_points_definition_path,
            disable_hallucination_detection=disable_hallucination_detection,
            investment=investment,
            n_round=n_round,
            re_examine_pass_check_points=False,  # 默认不重新检查已通过的检查点
        ))
        
        print(f"文档检查完成，详细报告已保存到 {output_path}")
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        traceback_info = traceback.format_exc()
        print(traceback_info)
        
        # 保存错误信息到文件
        try:
            import datetime
            error_file = "document_checker_error.log"
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write(f"错误发生时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"错误信息: {str(e)}\n\n")
                f.write(f"详细堆栈: \n{traceback_info}")
            print(f"错误详情已保存到 {error_file}")
        except:
            print("保存错误日志时出错")


if __name__ == "__main__":
    fire.Fire(main)  # 运行方式：python document_checker.py --document_path="requirements.md" --output_path="check_report.md" --check_points_definition_path="check_points_definition.json" --investment=10.0 --n_round=15 --disable_filtering=False --disable_hallucination_detection=False 