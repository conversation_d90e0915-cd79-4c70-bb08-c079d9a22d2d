"""
类归档代码分析器使用示例
作者: yuanminggui
创建时间: 2024-03-09
"""

import asyncio
from pathlib import Path
from code_analyzer_v2 import analyze_single_file, main, ClassArchiveManager


async def example_analyze_single_file():
    """示例：分析单个文件"""
    print("=== 分析单个文件示例 ===")
    
    # 分析当前文件
    current_file = __file__
    archive = await analyze_single_file(current_file, enable_llm=True)
    
    print(f"分析完成！发现 {len(archive.classes)} 个类")
    for class_name, class_info in archive.classes.items():
        print(f"- 类名: {class_name}")
        print(f"  方法数: {len(class_info.methods)}")
        print(f"  调用关系: {len(class_info.method_calls)}")
        if class_info.analysis_result:
            print(f"  LLM分析: 已完成")


async def example_analyze_directory():
    """示例：分析整个目录"""
    print("\n=== 分析目录示例 ===")
    
    # 分析当前目录
    current_dir = Path(__file__).parent
    await main(
        target_path=str(current_dir),
        output_file="directory_analysis_report.md",
        enable_class_archive=True,
        enable_llm_analysis=True
    )
    
    print("目录分析完成！")


async def example_load_and_view_archive():
    """示例：加载和查看已保存的归档"""
    print("\n=== 加载归档示例 ===")
    
    archive_manager = ClassArchiveManager()
    archive_dir = Path("class_archives")
    
    if archive_dir.exists():
        # 查找最新的归档文件
        archive_files = list(archive_dir.glob("*.json"))
        if archive_files:
            latest_archive = max(archive_files, key=lambda x: x.stat().st_mtime)
            print(f"加载归档文件: {latest_archive}")
            
            # 加载归档
            archive = archive_manager.load_archive(str(latest_archive))
            
            print(f"归档信息:")
            print(f"- 文件路径: {archive.file_info.get('file_path', 'Unknown')}")
            print(f"- 类数量: {len(archive.classes)}")
            print(f"- 创建时间: {archive.created_at}")
            print(f"- 更新时间: {archive.updated_at}")
            
            # 显示类信息
            for class_name, class_info in archive.classes.items():
                print(f"\n类: {class_name}")
                print(f"  - 行号: {class_info.start_line}-{class_info.end_line}")
                print(f"  - 方法: {list(class_info.methods.keys())}")
                print(f"  - 哈希值: {class_info.hash_value}")
                
                # 显示方法调用关系
                if class_info.method_calls:
                    print(f"  - 方法调用:")
                    for call in class_info.method_calls[:3]:  # 只显示前3个
                        print(f"    * {call.caller_method} -> {call.called_method} ({call.call_type})")
        else:
            print("未找到归档文件")
    else:
        print("归档目录不存在")


def example_view_generated_docs():
    """示例：查看生成的文档"""
    print("\n=== 查看生成的文档 ===")
    
    # 查找生成的文档目录
    current_dir = Path(".")
    doc_dirs = list(current_dir.glob("class_docs_*"))
    
    if doc_dirs:
        for doc_dir in doc_dirs:
            print(f"\n文档目录: {doc_dir}")
            
            # 列出文档文件
            md_files = list(doc_dir.glob("*.md"))
            for md_file in md_files:
                print(f"  - {md_file.name}")
                
                # 显示文件大小
                size = md_file.stat().st_size
                print(f"    大小: {size} 字节")
    else:
        print("未找到生成的文档目录")


class ExampleClass:
    """示例类，用于演示类归档功能"""
    
    def __init__(self, name: str):
        """初始化示例类"""
        self.name = name
        self.data = []
    
    def add_data(self, item: str) -> None:
        """添加数据"""
        self.data.append(item)
        self._log_action("add", item)
    
    def get_data(self) -> list:
        """获取数据"""
        self._log_action("get", None)
        return self.data.copy()
    
    def _log_action(self, action: str, item: str) -> None:
        """记录操作日志"""
        print(f"[{self.name}] {action}: {item}")
    
    def process_data(self) -> str:
        """处理数据"""
        result = f"Processed {len(self.data)} items"
        self._log_action("process", result)
        return result


class DataProcessor(ExampleClass):
    """数据处理器，继承自ExampleClass"""
    
    def __init__(self, name: str, processor_type: str):
        """初始化数据处理器"""
        super().__init__(name)
        self.processor_type = processor_type
    
    def advanced_process(self) -> dict:
        """高级处理"""
        # 调用父类方法
        basic_result = self.process_data()
        
        # 调用自己的方法
        filtered_data = self._filter_data()
        
        return {
            'basic': basic_result,
            'filtered': filtered_data,
            'type': self.processor_type
        }
    
    def _filter_data(self) -> list:
        """过滤数据"""
        # 这里会调用父类的get_data方法
        data = self.get_data()
        return [item for item in data if len(item) > 3]


async def main_example():
    """主示例函数"""
    print("代码分析器类归档功能演示")
    print("=" * 50)
    
    # 1. 分析单个文件
    await example_analyze_single_file()
    
    # 2. 分析目录（可选，注释掉以避免长时间运行）
    # await example_analyze_directory()
    
    # 3. 加载和查看归档
    await example_load_and_view_archive()
    
    # 4. 查看生成的文档
    example_view_generated_docs()
    
    print("\n演示完成！")
    print("请查看生成的文档目录和归档文件。")


if __name__ == "__main__":
    asyncio.run(main_example())
