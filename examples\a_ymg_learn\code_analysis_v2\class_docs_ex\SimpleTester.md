# 类: SimpleTester

**文件路径**: d:\bigModel\curser\stock_test1\backend\MetaGPT\examples\a_ymg_learn\ex.py
**行号范围**: 86-106
**最后更新**: 2025-05-27 09:53:30
**继承**: Role

## 方法列表
### __init__
- **行号**: 90-94
- **参数**: self
### _act
- **行号**: 96-106
- **参数**: self

## 方法调用关系
### SELF 调用
- SimpleTester.__init__ → SimpleTester.set_actions (行 92)
- SimpleTester.__init__ → SimpleTester._watch (行 94)
- SimpleTester._act → SimpleTester.get_memories (行 101)
### STATIC 调用
- SimpleTester.__init__ → super (行 91)
- SimpleTester._act → Message (行 104)
- SimpleTester._act → type (行 104)
### EXTERNAL 调用
- SimpleTester._act → logger.info (行 97)
- SimpleTester._act → todo.run (行 103)

## 源代码
```python
class SimpleTester(Role):
    name: str = "<PERSON>"
    profile: str = "SimpleTester"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.set_actions([SimpleWriteTest])
        # self._watch([SimpleWriteCode])
        self._watch([SimpleWriteCode, SimpleWriteReview])  # feel free to try this too

    async def _act(self) -> Message:
        logger.info(f"{self._setting}: to do {self.rc.todo}({self.rc.todo.name})")
        todo = self.rc.todo

        # context = self.get_memories(k=1)[0].content # use the most recent memory as context
        context = self.get_memories()  # use all memories as context

        code_text = await todo.run(context, k=5)  # specify arguments
        msg = Message(content=code_text, role=self.profile, cause_by=type(todo))

        return msg
```

## 智能分析
分析出错: Error communicating with LLM