#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件分析器角色
作者: Assistant
创建时间: 2024-03-14
"""

import json
from pathlib import Path
from typing import Optional

from metagpt.actions import Action
from metagpt.logs import logger
from metagpt.schema import Message

from .base_analyzer import BaseAnalyzer
from ..schema import AnalysisMessage, FileInfo

class ParseFileAction(Action):
    """解析文件动作"""
    name: str = "ParseFileAction"

    async def run(self, context: str) -> str:
        """执行文件分析"""
        try:
            # 解析配置
            config = json.loads(context)
            target_path = config.get("target_path")
            
            if not target_path:
                return json.dumps({
                    "error": "未指定目标路径",
                    "phase": "error"
                })
            
            # 分析文件
            path = Path(target_path)
            if not path.exists():
                return json.dumps({
                    "error": f"路径不存在: {target_path}",
                    "phase": "error"
                })
            
            # 读取文件内容
            if path.is_file():
                if not str(path).endswith('.py'):
                    return json.dumps({
                        "error": f"不是Python文件: {target_path}",
                        "phase": "error"
                    })
                    
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    file_info = FileInfo(
                        file_path=str(path),
                        content=content,
                        size=path.stat().st_size
                    )
                    
                    return json.dumps({
                        "status": "success",
                        "phase": "file_analyzed",
                        "file": file_info.dict()
                    })
                    
                except Exception as e:
                    return json.dumps({
                        "error": f"读取文件失败: {str(e)}",
                        "phase": "error"
                    })
            
            # 如果是目录，递归分析
            elif path.is_dir():
                files = []
                for py_file in path.glob('**/*.py'):
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        file_info = FileInfo(
                            file_path=str(py_file),
                            content=content,
                            size=py_file.stat().st_size
                        )
                        files.append(file_info.dict())
                        
                    except Exception as e:
                        logger.error(f"读取文件 {py_file} 失败: {str(e)}")
                        continue
                
                if not files:
                    return json.dumps({
                        "error": f"目录中没有Python文件: {target_path}",
                        "phase": "error"
                    })
                
                return json.dumps({
                    "status": "success",
                    "phase": "files_analyzed",
                    "files": files
                })
            
            return json.dumps({
                "error": f"无效的路径类型: {target_path}",
                "phase": "error"
            })
            
        except json.JSONDecodeError:
            return json.dumps({
                "error": "无效的配置格式",
                "phase": "error"
            })
        except Exception as e:
            return json.dumps({
                "error": f"分析过程出错: {str(e)}",
                "phase": "error"
            })

class FileAnalyzer(BaseAnalyzer):
    """文件分析器角色"""
    
    name: str = "FileAnalyzer"
    profile: str = "FileAnalyzer"
    goal: str = "分析代码文件结构"
    constraints: str = "只分析Python文件"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.set_actions([ParseFileAction])
        logger.info(f"{self.profile} 初始化完成，设置动作: {[action.name for action in self.actions]}")
        
    async def _observe(self, msg: Message) -> bool:
        """观察消息是否需要处理"""
        logger.info(f"{self.profile} 观察消息: phase={msg.phase}, role={msg.role}")
        
        if not isinstance(msg, AnalysisMessage):
            logger.warning(f"{self.profile} 收到非分析消息，忽略")
            return False
            
        # 检查是否是目标处理者
        if self.profile not in msg.processor:
            logger.info(f"{self.profile} 不是消息处理者 {msg.processor}，忽略")
            return False
            
        # 将消息添加到记忆
        self.rc.memory.add(msg)
        logger.info(f"{self.profile} 记住消息: {msg.content[:100]}")
        return True
        
    async def _process(self, msg: Message) -> Optional[Message]:
        """处理消息"""
        logger.info(f"{self.profile} 处理消息: phase={msg.phase}, role={msg.role}")
        
        if not isinstance(msg, AnalysisMessage):
            logger.warning(f"{self.profile} 收到非分析消息，忽略")
            return None
            
        # 处理初始化阶段的消息
        if msg.phase == "init":
            logger.info(f"{self.profile} 处理初始化消息")
            try:
                # 解析配置
                config = json.loads(msg.content)
                target_path = config.get("target_path")
                
                if not target_path:
                    logger.error(f"{self.profile} 配置中缺少目标路径")
                    return self._create_error_message("配置中缺少目标路径")
                    
                # 设置动作的上下文
                self.rc.todo = ParseFileAction()
                
                # 执行文件分析
                result = await self._act()
                logger.info(f"{self.profile} 分析完成，结果长度: {len(result.content)}")
                
                # 解析结果
                try:
                    data = json.loads(result.content)
                    phase = data.get("phase", "error")
                    error = data.get("error")
                    
                    if error:
                        logger.error(f"{self.profile} 分析出错: {error}")
                        self.update_state(phase="error", error=error)
                        return self._create_error_message(error)
                    
                    # 更新状态
                    self.update_state(phase=phase)
                    
                    # 设置下一个处理者
                    if phase in ["file_analyzed", "files_analyzed"]:
                        result.processor = {"StructureAnalyzer"}
                        result.phase = phase
                        logger.info(f"{self.profile} 分析成功，交给结构分析器处理")
                    
                    return result
                    
                except json.JSONDecodeError as e:
                    error_msg = f"结果解析失败: {str(e)}"
                    logger.error(f"{self.profile} {error_msg}")
                    self.update_state(phase="error", error=error_msg)
                    return self._create_error_message(error_msg)
                    
            except Exception as e:
                error_msg = f"处理消息时出错: {str(e)}"
                logger.error(f"{self.profile} {error_msg}")
                import traceback
                logger.error(traceback.format_exc())
                self.update_state(phase="error", error=error_msg)
                return self._create_error_message(error_msg)
        
        logger.info(f"{self.profile} 忽略非初始化阶段消息: {msg.phase}")
        return None
        
    def _create_error_message(self, error: str) -> AnalysisMessage:
        """创建错误消息"""
        return AnalysisMessage(
            content=json.dumps({"error": error, "phase": "error"}),
            role=self.profile,
            cause_by=type(self.rc.todo) if self.rc.todo else None,
            phase="error",
            processor=set(),
            dependencies=set()
        ) 