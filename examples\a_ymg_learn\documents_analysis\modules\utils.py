"""
文件名: MetaGPT/examples/a_ymg_learn/documents_analysis/modules/utils.py
创建日期: 2024年
描述: 文档检查系统中的实用工具函数
"""

import json
import logging
import os
import re
import traceback
from typing import Dict, List, Optional, Tuple, Union

from metagpt.logs import logger

def read_document_file(document_path: str) -> Tuple[str, bool]:
    """读取文档文件内容
    
    Args:
        document_path: 文档文件路径
    
    Returns:
        Tuple[str, bool]: (文档内容, 是否成功)
    """
    if not os.path.exists(document_path):
        logger.error(f"错误: 需求文档文件不存在: {document_path}")
        return "", False
    
    # 尝试读取文件内容
    try:
        with open(document_path, 'r', encoding='utf-8') as f:
            document_content = f.read()
            logger.info(f"成功读取文档内容，大小: {len(document_content)} 字符")
            return document_content, True
    except UnicodeDecodeError:
        # 尝试不同的编码
        try:
            with open(document_path, 'r', encoding='gbk') as f:
                document_content = f.read()
                logger.info(f"使用GBK编码成功读取文档内容，大小: {len(document_content)} 字符")
                return document_content, True
        except Exception as e:
            logger.error(f"错误: 无法读取需求文档文件: {e}")
            return "", False
    except Exception as e:
        logger.error(f"错误: 无法读取需求文档文件: {e}")
        return "", False

def read_json_file(file_path: str) -> Tuple[Union[List, Dict, None], bool]:
    """读取JSON文件并解析内容
    
    Args:
        file_path: JSON文件路径
    
    Returns:
        Tuple[Union[List, Dict, None], bool]: (解析后的JSON对象, 是否成功)
    """
    if not os.path.exists(file_path):
        logger.warning(f"文件不存在: {file_path}")
        return None, False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            logger.info(f"成功读取文件，大小: {len(content)} 字符")
            
            # 检查有效性
            if not content.strip():
                logger.warning(f"文件为空: {file_path}")
                return None, False
            
            # 预处理
            if content.startswith('\ufeff'):
                content = content[1:]
                logger.info("移除了BOM标记")
            
            try:
                json_data = json.loads(content)
                logger.info(f"成功解析JSON数据，类型: {type(json_data).__name__}")
                return json_data, True
            except json.JSONDecodeError as je:
                logger.error(f"JSON解析失败: {je}, 位置: {je.pos}, 行: {je.lineno}")
                
                # 尝试修复和重新解析
                fixed_content = fix_json_content(content)
                try:
                    json_data = json.loads(fixed_content)
                    logger.info("修复后成功解析JSON数据")
                    return json_data, True
                except json.JSONDecodeError:
                    logger.error(f"修复后仍然无法解析JSON文件: {file_path}")
                    return None, False
    except Exception as e:
        logger.error(f"读取文件时出错: {e}")
        logger.error(traceback.format_exc())
        return None, False

def fix_json_content(content: str) -> str:
    """尝试修复损坏的JSON内容
    
    Args:
        content: 可能损坏的JSON字符串
    
    Returns:
        str: 尝试修复后的JSON字符串
    """
    # 替换单引号为双引号
    fixed_content = content.replace("'", '"')
    
    # 替换JS风格注释
    fixed_content = re.sub(r'//.*?\n', '\n', fixed_content)
    fixed_content = re.sub(r'/\*.*?\*/', '', fixed_content, flags=re.DOTALL)
    
    # 移除尾部可能残留的逗号
    fixed_content = re.sub(r',\s*}', '}', fixed_content)
    fixed_content = re.sub(r',\s*\]', ']', fixed_content)
    
    # 尝试提取JSON部分
    json_start = fixed_content.find('[')
    json_start2 = fixed_content.find('{')
    
    if json_start == -1 and json_start2 == -1:
        logger.warning("无法在内容中找到JSON开始标记")
        return fixed_content
    
    # 使用最先出现的有效JSON开始标记
    if json_start == -1:
        start_pos = json_start2
    elif json_start2 == -1:
        start_pos = json_start
    else:
        start_pos = min(json_start, json_start2)
    
    if start_pos > 0:
        fixed_content = fixed_content[start_pos:]
        logger.info(f"截取了JSON内容，跳过了前 {start_pos} 个字符")
    
    return fixed_content

def clean_check_points_list(check_points: List[str]) -> List[str]:
    """清理和去重检查点列表
    
    Args:
        check_points: 原始检查点列表
    
    Returns:
        List[str]: 去重后的检查点列表
    """
    # 去除检查点列表中的重复项
    unique_check_points = []
    seen_check_points = set()
    
    for cp in check_points:
        if cp not in seen_check_points:
            unique_check_points.append(cp)
            seen_check_points.add(cp)
        else:
            logger.warning(f"检查点列表中存在重复项: {cp}")
    
    if len(unique_check_points) < len(check_points):
        logger.info(f"去除了 {len(check_points) - len(unique_check_points)} 个重复检查点")
    
    return unique_check_points

def get_default_check_points() -> List[str]:
    """获取默认的检查点列表，尝试从check_points_definition.json读取
    
    Returns:
        List[str]: 默认检查点列表
    """
    # 尝试从检查点定义文件读取
    possible_paths = [
        "check_points_definition.json",
        "../check_points_definition.json",
        "../../check_points_definition.json",
        "examples/a_ymg_learn/documents_analysis/check_points_definition.json"
    ]
    
    # 查找可用的定义文件
    definition_file = None
    for path in possible_paths:
        if os.path.exists(path):
            definition_file = path
            logger.info(f"找到检查点定义文件: {path}")
            break
    
    # 如果找到定义文件，从中提取检查点
    if definition_file:
        try:
            json_data, success = read_json_file(definition_file)
            if success and json_data and isinstance(json_data, list):
                # 从检查点定义中提取检查点名称
                check_points = [item.get("name", "") for item in json_data if item.get("name")]
                if check_points:
                    logger.info(f"从检查点定义文件提取出 {len(check_points)} 个检查点")
                    return clean_check_points_list(check_points)
                else:
                    logger.warning("检查点定义文件无法提取有效的检查点名称")
            else:
                logger.warning(f"检查点定义文件格式错误或无法读取: {definition_file}")
        except Exception as e:
            logger.error(f"从定义文件读取检查点时出错: {e}")
            logger.error(traceback.format_exc())
    else:
        logger.warning("找不到检查点定义文件，使用硬编码默认值")
    
    # 如果无法从文件读取，使用硬编码的默认值
    logger.info("使用硬编码的默认检查点列表")
    return [
        "文档是否包含明确的功能需求",
        "是否定义了系统接口",
        "是否包含具体的截止时间",
        "是否包含开发人员信息"
    ]

def calculate_required_rounds(check_points: List[str]) -> int:
    """计算处理所有检查点所需的最小轮数
    
    Args:
        check_points: 检查点列表
        
    Returns:
        int: 所需的轮数
    """
    n_checkpoints = len(check_points)
    if n_checkpoints == 0:
        return 5  # 默认最小轮数
    
    # 每个检查点至少需要2轮来完成
    # 1轮用于解析专家发送检查点，1轮用于检查专家处理并返回结果
    # 如果启用了幻觉检测，可能需要额外的轮数
    # 加5是为了预留足够的轮数给其他处理，如总结等
    return n_checkpoints * 3 + 5

def prepare_output_file(output_path: str) -> bool:
    """准备输出文件，如果已存在则清除
    
    Args:
        output_path: 输出文件路径
    
    Returns:
        bool: 操作是否成功
    """
    if os.path.exists(output_path):
        try:
            os.remove(output_path)
            logger.info(f"已清除现有的输出文件: {output_path}")
            return True
        except Exception as e:
            logger.warning(f"无法清除现有的输出文件: {e}")
            return False
    return True 