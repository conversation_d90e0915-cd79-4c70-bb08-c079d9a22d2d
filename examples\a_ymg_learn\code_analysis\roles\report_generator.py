#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2024/3/11
<AUTHOR> ale<PERSON><PERSON><PERSON>
@File    : report_generator.py
"""
from datetime import datetime
from typing import Dict, List

from metagpt.schema import Message
from metagpt.logs import logger

from .base_analyzer import BaseAnalyzer
from ..schema import AnalysisMessage, AnalysisReport

class ReportGenerator(BaseAnalyzer):
    """报告生成器"""
    name: str = "ReportGenerator"
    description: str = "负责生成分析报告"
    
    async def _process(self, msg: Message) -> Message:
        """处理报告生成请求"""
        try:
            # 收集所有分析结果
            file_analysis = self._get_latest_result("FileAnalyzer")
            structure_analysis = self._get_latest_result("StructureAnalyzer")
            function_analysis = self._get_latest_result("FunctionAnalyzer")
            
            # 生成报告
            report = await self._generate_report(
                file_analysis,
                structure_analysis,
                function_analysis
            )
            
            # 更新状态
            self.update_state("completed", result={'report': report})
            
            # 发送报告
            return AnalysisMessage(
                role=self.name,
                content=report,
                phase="report",
                processor=set(),  # 报告是最终结果，不需要后续处理
                dependencies={"FileAnalyzer", "StructureAnalyzer", "FunctionAnalyzer"}
            )
            
        except Exception as e:
            error_msg = f"生成报告失败: {str(e)}"
            logger.error(error_msg)
            self.update_state("error", error=error_msg)
            return AnalysisMessage(
                role=self.name,
                content={"error": error_msg},
                phase="error",
                processor={self.name}
            )
            
    def _get_latest_result(self, analyzer_name: str) -> Dict:
        """获取分析器的最新结果"""
        if hasattr(self.rc.env, "get_state"):
            state = self.rc.env.get_state(analyzer_name)
            if state and state.result:
                return state.result
        return {}
        
    async def _generate_report(
        self,
        file_analysis: Dict,
        structure_analysis: Dict,
        function_analysis: Dict
    ) -> str:
        """生成Markdown格式的报告"""
        report = [
            "# 代码分析报告",
            f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        ]
        
        # 添加文件分析结果
        report.append("## 文件分析结果")
        if file_analysis:
            report.append(f"\n- 总文件数: {file_analysis.get('total_files', 0)}")
            if 'files' in file_analysis:
                report.append("\n### 文件列表")
                for file_info in file_analysis['files']:
                    report.append(f"- {file_info['file_path']} ({file_info['size']} 字节)")
        else:
            report.append("\n*没有文件分析结果*")
            
        # 添加代码结构分析结果
        report.append("\n## 代码结构分析")
        if structure_analysis and 'structures' in structure_analysis:
            for file_path, structure in structure_analysis['structures'].items():
                report.append(f"\n### 文件: {file_path}")
                
                # 生成导入依赖图
                if 'imports' in structure:
                    report.append("\n#### 导入依赖")
                    report.append("```mermaid")
                    report.append("graph LR")
                    base_name = file_path.split('/')[-1].replace('.py', '')
                    for imp in structure['imports']:
                        report.append(f"    {base_name}-->{imp}")
                    report.append("```\n")
                    
                # 添加类信息
                if 'classes' in structure:
                    report.append("\n#### 类定义")
                    for cls in structure['classes']:
                        report.append(f"\n##### {cls['name']}")
                        report.append(f"- 位置: 第{cls['start_line']}行 - 第{cls['end_line']}行")
                        if 'methods' in cls:
                            report.append("\n###### 方法列表")
                            for method in cls['methods']:
                                report.append(f"- {method['name']} (第{method['start_line']}行 - 第{method['end_line']}行)")
                                
                # 添加函数信息
                if 'functions' in structure:
                    report.append("\n#### 全局函数")
                    for func in structure['functions']:
                        report.append(f"\n##### {func['name']}")
                        report.append(f"- 位置: 第{func['start_line']}行 - 第{func['end_line']}行")
        else:
            report.append("\n*没有代码结构分析结果*")
            
        # 添加函数分析结果
        report.append("\n## 函数分析")
        if function_analysis and 'functions' in function_analysis:
            for file_path, funcs in function_analysis['functions'].items():
                report.append(f"\n### 文件: {file_path}")
                for func_name, func_info in funcs.items():
                    report.append(f"\n#### 函数: {func_name}")
                    if 'analysis' in func_info:
                        report.append("\n" + func_info['analysis'])
        else:
            report.append("\n*没有函数分析结果*")
            
        # 添加统计信息
        report.append("\n## 统计信息")
        total_files = len(structure_analysis.get('structures', {}))
        total_classes = 0
        total_functions = 0
        total_methods = 0
        
        for structure in structure_analysis.get('structures', {}).values():
            total_classes += len(structure.get('classes', []))
            total_functions += len(structure.get('functions', []))
            for cls in structure.get('classes', []):
                total_methods += len(cls.get('methods', []))
                
        report.append("\n### 代码规模")
        report.append(f"- 总文件数: {total_files}")
        report.append(f"- 总类数: {total_classes}")
        report.append(f"- 总函数数: {total_functions}")
        report.append(f"- 总方法数: {total_methods}")
        
        # 生成代码分布图
        if total_classes > 0 or total_functions > 0 or total_methods > 0:
            report.append("\n### 代码分布")
            report.append("```mermaid")
            report.append("pie")
            report.append('    title 代码组成')
            report.append(f'    "类定义" : {total_classes}')
            report.append(f'    "全局函数" : {total_functions}')
            report.append(f'    "类方法" : {total_methods}')
            report.append("```")
            
        return "\n".join(report) 