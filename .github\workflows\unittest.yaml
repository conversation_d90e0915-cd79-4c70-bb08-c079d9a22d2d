name: Unit Tests

on:
  pull_request_target:
  push: 
    branches:
      - 'main'
      - 'dev'
      - '*-release'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        # python-version: ['3.9', '3.10', '3.11']
        python-version: ['3.9']

    steps:
    - uses: actions/checkout@v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .[test]
        npm install -g @mermaid-js/mermaid-cli
        playwright install --with-deps
    - name: Test with pytest
      run: |
        export ALLOW_OPENAI_API_CALL=0
        mkdir -p ~/.metagpt && cp tests/config2.yaml ~/.metagpt/config2.yaml
        pytest | tee unittest.txt
    - name: Show coverage report
      run: |
        coverage report -m
    - name: Show failed tests and overall summary
      run: |
        grep -E "FAILED tests|ERROR tests|[0-9]+ passed," unittest.txt
        failed_count=$(grep -E "FAILED tests|ERROR tests" unittest.txt | wc -l | tr -d '[:space:]')
        if [[ $failed_count -gt 0 ]]; then
          echo "$failed_count failed lines found! Task failed."
          exit 1
        fi
    - name: Upload pytest test results
      uses: actions/upload-artifact@v3
      with:
        name: pytest-results-${{ matrix.python-version }}
        path: |
          ./unittest.txt
          ./htmlcov/
          ./tests/data/rsp_cache_new.json
        retention-days: 3
      if: ${{ always() }}
