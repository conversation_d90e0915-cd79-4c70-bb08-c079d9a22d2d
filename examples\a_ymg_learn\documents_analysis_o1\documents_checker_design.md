# 需求文档检查系统设计文档

## 1. 系统概述

需求文档检查系统是一个基于MetaGPT框架构建的多智能体系统，用于自动化分析和检查需求文档是否满足特定的标准和要求。该系统由三个专家角色组成，分别是文档解析专家、检查专家和文档总结专家，它们通过消息传递机制协同工作，完成对文档的分析、检查和总结工作。

### 1.1 系统目标

- 自动化解析用户提供的需求文档
- 根据预设的检查点对文档进行全面评估
- 生成详细的检查报告，指出文档中已满足和未满足的要求点
- 提供改进建议以提高文档质量

### 1.2 系统架构

系统采用多智能体架构，每个智能体（专家）负责特定的任务，通过消息传递机制协同工作：

1. **文档解析专家**：接收用户输入的文档，进行解析和结构化，提取关键信息传递给检查专家
2. **检查专家**：接收解析结果，对照预设的检查点逐一评估，生成检查结果传递给总结专家
3. **文档总结专家**：汇总检查结果，形成最终报告，指出文档的优缺点和改进建议

## 2. 系统组件详细设计

### 2.1 核心组件

#### 2.1.1 Action（动作）

1. **ParseDocument**：文档解析动作，负责解析文档内容
2. **CheckDocument**：文档检查动作，对照检查点评估文档内容
3. **SummarizeDocument**：文档总结动作，生成最终报告

#### 2.1.2 Role（角色）

1. **DocumentParsingExpert**：文档解析专家角色，执行ParseDocument动作
2. **DocumentCheckingExpert**：文档检查专家角色，执行CheckDocument动作
3. **DocumentSummaryExpert**：文档总结专家角色，执行SummarizeDocument动作

#### 2.1.3 Team（团队）

团队组件负责协调各个角色之间的协作，管理消息传递，确保系统按照预定的流程运行。

### 2.2 数据流

1. 用户提供需求文档和检查点列表
2. 文档解析专家接收文档，进行解析
3. 解析结果通过消息传递给检查专家
4. 检查专家逐一评估检查点，生成检查结果
5. 检查结果通过消息传递给总结专家
6. 总结专家生成最终报告

## 3. 详细组件说明

### 3.1 动作（Actions）

#### 3.1.1 ParseDocument

**职责**：解析用户提供的需求文档，提取关键信息和内容结构。

**输入**：
- 需求文档内容（字符串或文件路径）

**输出**：
- 解析后的文档结构（JSON格式）
- 提取的关键信息列表

**提示模板**：
```
## 背景介绍
你是文档解析专家，需要解析用户提供的需求文档。

## 用户文档
{document_content}

## 文档解析任务
请分析上述文档，提取以下内容：
1. 文档的主要结构
2. 文档中描述的功能需求
3. 文档中描述的非功能需求
4. 文档中的关键术语及其定义
5. 文档中的约束条件

请以JSON格式返回解析结果。
```

#### 3.1.2 CheckDocument

**职责**：根据预设的检查点列表，评估解析后的文档是否符合要求。

**输入**：
- 解析后的文档结构
- 检查点列表

**输出**：
- 每个检查点的评估结果（满足/不满足/部分满足）
- 详细说明和证据

**提示模板**：
```
## 背景介绍
你是文档检查专家，需要根据预设的检查点评估解析后的需求文档。

## 解析后的文档
{parsed_document}

## 检查点
{check_point}

## 检查任务
请评估上述文档是否满足给定的检查点要求，并提供详细说明和证据。
请使用以下格式回答：

检查点：[检查点内容]
评估结果：[满足/不满足/部分满足]
详细说明：[说明文档是否满足该检查点，以及满足或不满足的具体证据]
```

#### 3.1.3 SummarizeDocument

**职责**：汇总所有检查点的评估结果，生成最终报告。

**输入**：
- 所有检查点的评估结果

**输出**：
- 综合评估报告
- 改进建议

**提示模板**：
```
## 背景介绍
你是文档总结专家，需要汇总所有检查点的评估结果，生成最终报告。

## 检查结果
{check_results}

## 总结任务
请根据以上检查结果，生成一份综合评估报告。报告应包含以下内容：
1. 总体评估概述
2. 已满足的检查点列表
3. 未满足的检查点列表
4. 部分满足的检查点列表
5. 具体的改进建议

请使用以下格式回答：

# 需求文档检查报告

## 总体评估
[总体评估内容]

## 满足的检查点
- [检查点1]
- [检查点2]
...

## 未满足的检查点
- [检查点1]：[简短说明为什么未满足]
- [检查点2]：[简短说明为什么未满足]
...

## 部分满足的检查点
- [检查点1]：[简短说明为什么部分满足]
- [检查点2]：[简短说明为什么部分满足]
...

## 改进建议
1. [建议1]
2. [建议2]
...
```

### 3.2 角色（Roles）

#### 3.2.1 DocumentParsingExpert

**职责**：接收用户提供的文档，执行解析动作，将解析结果传递给检查专家。

**相关动作**：ParseDocument

**观察对象**：UserRequirement（用户需求）

**行为流程**：
1. 从用户需求中获取文档内容
2. 执行ParseDocument动作解析文档
3. 将解析结果打包成消息传递给DocumentCheckingExpert

#### 3.2.2 DocumentCheckingExpert

**职责**：接收解析后的文档和检查点，执行检查动作，将检查结果传递给总结专家。

**相关动作**：CheckDocument

**观察对象**：ParseDocument动作的结果

**行为流程**：
1. 接收来自文档解析专家的解析结果
2. 获取预设的检查点列表
3. 逐一执行CheckDocument动作评估每个检查点
4. 将所有检查结果打包成消息传递给DocumentSummaryExpert

#### 3.2.3 DocumentSummaryExpert

**职责**：接收所有检查结果，执行总结动作，生成最终报告。

**相关动作**：SummarizeDocument

**观察对象**：CheckDocument动作的结果

**行为流程**：
1. 接收来自检查专家的所有检查结果
2. 执行SummarizeDocument动作生成最终报告
3. 将最终报告返回给用户

## 4. 系统流程

### 4.1 初始化阶段

1. 用户提供需求文档和检查点列表
2. 系统初始化三个专家角色
3. 系统创建团队并雇佣这三个专家

### 4.2 运行阶段

1. 系统将用户提供的文档和检查点作为输入启动项目
2. 文档解析专家接收文档并执行解析
3. 检查专家接收解析结果和检查点，逐一评估
4. 总结专家接收所有检查结果，生成最终报告
5. 系统将最终报告返回给用户

### 4.3 结束阶段

1. 系统释放资源
2. 记录运行日志

## 5. UML图

### 5.1 类图

```
+----------------+       +----------------+       +----------------+
|     Action     |       |     Role       |       |     Team       |
+----------------+       +----------------+       +----------------+
| - name         |       | - name         |       | - roles        |
| - PROMPT       |       | - profile      |       | - investment   |
+----------------+       | - actions      |       +----------------+
| + run()        |       | - memories     |       | + hire()       |
+----------------+       +----------------+       | + invest()     |
        ^                | + _act()       |       | + run_project()|
        |                | + _observe()   |       | + run()        |
        |                +----------------+       +----------------+
        |                        ^
        |                        |
+-------+--------+ +-------------+-------------+ +-----------------+
| ParseDocument  | | DocumentParsingExpert     | | UserRequirement |
+----------------+ +---------------------------+ +-----------------+
| - name         | | - name                    | | - content      |
| - PROMPT       | | - profile                 | +-----------------+
+----------------+ | - next_role               |
| + run()        | +---------------------------+
+----------------+ | + _act()                  |
                   | + _observe()              |
                   +---------------------------+
                             |
                             |
                   +---------------------------+
                   | Message                   |
                   +---------------------------+
                   | - content                 |
                   | - role                    |
                   | - cause_by                |
                   | - sent_from               |
                   | - send_to                 |
                   +---------------------------+
```

### 5.2 流程图

```
+---------------+     +---------------------+     +---------------------+     +---------------------+
| 用户          |     | 文档解析专家        |     | 文档检查专家        |     | 文档总结专家        |
+-------+-------+     +-----------+---------+     +-----------+---------+     +-----------+---------+
        |                         |                           |                           |
        | 提供文档和检查点        |                           |                           |
        +------------------------>|                           |                           |
        |                         |                           |                           |
        |                         | 解析文档                  |                           |
        |                         +-------------------------->|                           |
        |                         |                           |                           |
        |                         |                           | 检查文档                  |
        |                         |                           +-------------------------->|
        |                         |                           |                           |
        |                         |                           |                           | 生成报告
        |<-------------------------------------------------------------------+-----------+
        |                         |                           |                           |
+-------+-------+     +-----------+---------+     +-----------+---------+     +-----------+---------+
```

## 6. 使用示例

```python
# 示例代码，展示如何使用该系统
async def check_requirements_document(document_content: str, check_points: list, investment: float = 3.0, n_round: int = 3):
    # 创建三个专家角色
    parsing_expert = DocumentParsingExpert()
    checking_expert = DocumentCheckingExpert(check_points=check_points)
    summary_expert = DocumentSummaryExpert()
    
    # 组建团队
    team = Team()
    team.hire([parsing_expert, checking_expert, summary_expert])
    team.invest(investment)
    
    # 启动项目
    idea = {"document": document_content, "check_points": check_points}
    team.run_project(idea, send_to="DocumentParsingExpert")
    
    # 运行团队
    await team.run(n_round=n_round)
```

## 7. 后续开发计划

1. 增加对不同格式文档的支持（Markdown、Word、PDF等）
2. 实现预设检查点模板库，支持不同类型文档的检查
3. 优化检查算法，提高准确性
4. 添加用户界面，方便使用
5. 支持批量文档检查
6. 添加检查结果的历史记录和比较功能 