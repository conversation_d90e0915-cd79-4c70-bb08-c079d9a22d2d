# 九天大模型使用指南

## 简介

九天（JiuTian）是中国移动提供的大模型平台，提供了丰富的AI能力。本指南将帮助您在MetaGPT框架中配置和使用九天大模型。

## 前提条件

在使用九天大模型前，您需要：

1. 前往 [九天平台](https://jiutian.10086.cn/) 注册账号
2. 获取API密钥，格式为：`id.secret`
3. 了解可用的模型名称和参数

## 安装

确保您已安装好MetaGPT，并且安装了必要的依赖：

```bash
pip install PyJWT~=2.8.0  # 用于JiuTian API鉴权
```

## 配置方法

### 方法一：使用环境变量

您可以通过设置环境变量来配置九天大模型：

```bash
# Windows
set JIUTIAN_API_KEY=your_api_key_id.your_api_secret
set JIUTIAN_MODEL=jiutian-general

# Linux/MacOS
export JIUTIAN_API_KEY=your_api_key_id.your_api_secret
export JIUTIAN_MODEL=jiutian-general
```

### 方法二：在config2.yaml中配置

在您的MetaGPT配置文件（通常是`config/config2.yaml`）中添加：

```yaml
llm:
  api_type: "jiutian"
  api_key: "your_api_key_id.your_api_secret"
  model: "jiutian-general"
  base_url: "https://jiutian.10086.cn/api/v1/services"  # 可选，使用默认值即可
```

### 方法三：在代码中直接配置

您可以在代码中直接创建配置：

```python
from metagpt.configs.llm_config import LLMConfig, LLMType

config = LLMConfig(
    api_key="your_api_key_id.your_api_secret",
    api_type=LLMType.JIUTIAN,
    model="jiutian-general",
    # 以下为可选参数
    # base_url="https://jiutian.10086.cn/api/v1/services",
    # max_token=2048,
    # temperature=0.5,
    # top_p=0.95,
)
```

## 使用示例

以下是使用九天大模型的简单示例：

```python
import asyncio
from metagpt.configs.llm_config import LLMConfig, LLMType
from metagpt.provider.jiutian_api import JiuTianLLM

async def main():
    # 配置
    config = LLMConfig(
        api_key="your_api_key_id.your_api_secret",
        api_type=LLMType.JIUTIAN,
        model="jiutian-general",
    )
    
    # 初始化
    jiutian_llm = JiuTianLLM(config)
    
    # 使用
    response = await jiutian_llm.aask("你好，请介绍一下你自己")
    print(response)

if __name__ == "__main__":
    asyncio.run(main())
```

更多详细示例请参考 `examples/jiutian_example.py`。

## 支持的参数

九天大模型支持以下参数：

- `model`: 模型名称，例如 "jiutian-general"
- `max_tokens`: 生成的最大token数量，默认2048
- `temperature`: 温度参数，控制随机性，默认0.5
- `top_p`: 核采样概率，默认0.95
- `stream`: 是否启用流式响应，默认True

## 错误排查

如果遇到问题，请检查：

1. API密钥格式是否正确（应为 `id.secret` 格式）
2. 网络连接是否通畅
3. 模型名称是否正确
4. 请求参数是否在合理范围内

## 完整API文档

九天大模型API完整文档请参考：[九天大模型应用平台API文档](https://jiutian.10086.cn/portal/common-helpcenter#/document/287?platformCode=LLM_STUDIO) 