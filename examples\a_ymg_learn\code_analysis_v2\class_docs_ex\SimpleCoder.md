# 类: SimpleCoder

**文件路径**: d:\bigModel\curser\stock_test1\backend\MetaGPT\examples\a_ymg_learn\ex.py
**行号范围**: 56-63
**最后更新**: 2025-05-27 09:53:30
**继承**: Role

## 方法列表
### __init__
- **行号**: 60-63
- **参数**: self

## 方法调用关系
### SELF 调用
- SimpleCoder.__init__ → SimpleCoder._watch (行 62)
- SimpleCoder.__init__ → SimpleCoder.set_actions (行 63)
### STATIC 调用
- SimpleCoder.__init__ → super (行 61)

## 源代码
```python
class SimpleCoder(Role):
    name: str = "Alice"
    profile: str = "SimpleCoder"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._watch([UserRequirement])
        self.set_actions([SimpleWriteCode])
```

## 智能分析
分析出错: Error communicating with LLM