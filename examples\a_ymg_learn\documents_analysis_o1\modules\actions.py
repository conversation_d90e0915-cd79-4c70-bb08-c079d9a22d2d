"""
文件名: MetaGPT/examples/a_ymg_learn/documents_analysis/modules/actions.py
创建日期: 2024年
描述: 文档检查系统中的所有Action类实现
"""

import json
import logging
import re
import os
from typing import Dict, List, ClassVar

from metagpt.actions import Action
from metagpt.logs import logger

class ParseDocument(Action):
    """动作：文档解析专家解析文档"""

    # 基础提示模板，将在run方法中根据check_points_definition.json动态扩展
    PROMPT_TEMPLATE: str = """
    ## 背景介绍
    你是文档解析专家，需要解析用户提供的需求文档。

    ## 用户文档
    {document_content}

    ## 文档解析任务
    请详细分析上述文档，提取以下内容（仅限这些内容）：
    1. 文档的主要结构和组织方式
    {extraction_fields}

    特别提示：
    - 对于日期和时间信息，请特别注意准确提取，例如"2025年4月10日前完成"这样的表述。
    - 如果文档中某项内容不存在，请明确标注为"不存在"，不要使用空数组或空对象。
    - 请基于文档的实际内容进行解析，不要添加自己的假设或推断。
    - 返回的JSON必须是有效的格式，不要包含注释或其他非JSON元素。
    - 不要在JSON中使用占位符（如"..."、"等"）。每个列表必须明确列出所有项目，或者明确标明"不存在"。

    请以JSON格式返回解析结果，保证格式的有效性，不要包含任何非JSON内容。不要使用markdown代码块，直接返回纯JSON字符串。

    示例输出格式（注意这只是格式示例，实际内容应根据你的解析结果填充）：
    {{
      "document_structure": "文档分为X个部分，包括引言、功能需求等...",
      {example_fields}
    }}

    如果某些内容在文档中不存在，请这样标注：
    {{
      "document_structure": "文档分为简介和系统功能两部分",
      {empty_fields}
    }}

    重要提示：只返回纯JSON格式，不要有任何额外说明，也不要使用```json和```这样的标记。请确保你的JSON是有效的，所有字符串都用双引号，不要使用任何占位符或省略号。
    """
    name: str = "ParseDocument"
    
    # 检查点ID到解析字段的映射
    CHECKPOINT_TO_FIELD_MAP: ClassVar[Dict[str, Dict[str, str]]] = {
        "functionality_requirements": {
            "field": "functional_requirements",
            "description": "文档中描述的功能需求（详细列出每一条）",
            "example": '"functional_requirements": ["系统应提供用户登录功能", "系统应支持数据导出功能"]'
        },
        "interface_definitions": {
            "field": "system_interfaces",
            "description": "文档中定义的系统接口（如API、调用方式等）",
            "example": '"system_interfaces": ["RESTful API接口: POST /api/login, 参数: username, password", "数据库接口: MySQL连接"]'
        },
        "deadline_specifications": {
            "field": "deadlines",
            "description": "文档中提到的截止时间（如日期、期限等）",
            "example": '"deadlines": ["2025年4月10日前完成系统开发"]'
        },
        "developer_information": {
            "field": "developer_information",
            "description": "文档中包含的开发人员信息（如姓名、角色、联系方式等）",
            "example": '"developer_information": ["项目经理: 张三 (电话: 123456789)", "开发工程师: 李四 (邮箱: <EMAIL>)"]'
        }
    }

    # 检查点列表
    check_points_list: List[str] = []

    def _build_dynamic_prompt(self, document_content: str, local_check_points: List[str] = None) -> str:
        """根据检查点列表动态构建提示模板
        
        Args:
            document_content: 文档内容
            local_check_points: 检查点列表，如果提供则优先使用
            
        Returns:
            str: 构建好的提示模板
        """
        # 1. 构建要提取的字段描述
        extraction_fields = []
        example_fields = []
        empty_fields = []
        field_index = 2  # 从2开始，因为文档结构已经是第1项
        
        # 使用传入的检查点列表或类变量
        check_points_to_use = local_check_points if local_check_points is not None else self.check_points_list
        
        # 每个ID到字段的映射
        id_to_field = {}
        name_to_id = {}
        
        # 尝试从JSON定义文件中加载检查点ID映射
        try:
            # 查找当前目录及上级目录中的check_points_definition.json
            possible_paths = [
                "check_points_definition.json",
                "../check_points_definition.json",
                "../../check_points_definition.json",
                "examples/a_ymg_learn/documents_analysis/check_points_definition.json"
            ]
            
            def_file_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    def_file_path = path
                    break
            
            if def_file_path:
                logger.info(f"尝试从 {def_file_path} 加载检查点定义")
                from .utils import read_json_file
                check_points_defs, success = read_json_file(def_file_path)
                
                if success and check_points_defs and isinstance(check_points_defs, list):
                    # 构建检查点名称到ID的映射
                    for def_item in check_points_defs:
                        if isinstance(def_item, dict) and 'id' in def_item and 'name' in def_item:
                            name_to_id[def_item['name']] = def_item['id']
                            logger.info(f"映射检查点 '{def_item['name']}' 到ID '{def_item['id']}'")
        except Exception as e:
            logger.warning(f"加载检查点定义时出错: {e}")
        
        # 对于检查点列表中的每个检查点，添加相应的字段
        logger.info(f"使用检查点列表构建提取字段，共 {len(check_points_to_use)} 个检查点")
        for check_point in check_points_to_use:
            # 尝试找到检查点对应的ID
            checkpoint_id = name_to_id.get(check_point)
            field_info = None
            
            if checkpoint_id and checkpoint_id in self.CHECKPOINT_TO_FIELD_MAP:
                field_info = self.CHECKPOINT_TO_FIELD_MAP[checkpoint_id]
                logger.info(f"检查点 '{check_point}' (ID: {checkpoint_id}) 映射到字段 '{field_info['field']}'")
            else:
                # 尝试通过关键词匹配
                for id_key, field_data in self.CHECKPOINT_TO_FIELD_MAP.items():
                    key_words = id_key.split('_')
                    if any(word.lower() in check_point.lower() for word in key_words if len(word) > 3):
                        field_info = field_data
                        logger.info(f"关键词匹配：检查点 '{check_point}' 映射到字段 '{field_data['field']}'")
                        break
            
            if field_info:
                # 添加字段到提取列表
                extraction_fields.append(f"{field_index}. {field_info['description']}")
                example_fields.append(f'"{field_info["field"]}": {field_info["example"]}')
                empty_fields.append(f'"{field_info["field"]}": "不存在"')
                id_to_field[field_info["field"]] = True
                field_index += 1
            else:
                logger.warning(f"无法为检查点 '{check_point}' 找到对应的字段映射")
        
        # 如果没有找到任何有效的字段映射，使用默认字段
        if not id_to_field:
            logger.warning("没有找到任何有效的字段映射，使用所有默认字段")
            for field_data in self.CHECKPOINT_TO_FIELD_MAP.values():
                extraction_fields.append(f"{field_index}. {field_data['description']}")
                example_fields.append(f'"{field_data["field"]}": {field_data["example"]}')
                empty_fields.append(f'"{field_data["field"]}": "不存在"')
                field_index += 1
        
        # 构建最终的提示模板
        prompt = self.PROMPT_TEMPLATE.format(
            document_content=document_content,
            extraction_fields="\n".join(extraction_fields),
            example_fields=",\n      ".join(example_fields),
            empty_fields=",\n      ".join(empty_fields)
        )
        
        return prompt

    async def run(self, document_content: str, check_points: List[str] = None):
        """执行文档解析

        Args:
            document_content: 需求文档内容
            check_points: 检查点列表，可选，如果提供则优先使用

        Returns:
            str: 解析后的文档结构（JSON格式字符串）
        """
        # 使用传入的检查点列表（如果有）
        if check_points:
            local_check_points = check_points
        else:
            local_check_points = self.check_points_list
            
        # 根据检查点动态构建提示模板
        prompt = self._build_dynamic_prompt(document_content, local_check_points)
        logger.info(f"文档解析提示构建完成，长度: {len(prompt)} 字符")
        
        try:
            # 尝试多次解析，如果失败则重试
            max_attempts = 2
            for attempt in range(max_attempts):
                try:
                    # 请求AI解析文档
                    rsp = await self._aask(prompt)
                    
                    # 记录返回的前100个字符，用于调试
                    logger.info(f"AI返回结果前100个字符: {rsp[:100] if rsp else '空'}")
                    
                    # 处理返回结果，确保是有效的JSON
                    if not rsp or rsp.strip() == "":
                        logger.warning("AI返回了空结果，使用空JSON对象")
                        return "{}"
                    
                    # 预处理：清理常见的非JSON内容
                    clean_rsp = rsp.strip()
                    
                    # 1. 移除Markdown代码块标记
                    # 处理 ```json 或 ``` 开头的情况
                    if clean_rsp.startswith("```"):
                        # 找到第一个和最后一个 ``` 标记
                        start_pos = clean_rsp.find("```")
                        # 检查是否有语言标识
                        if "json" in clean_rsp[start_pos:start_pos+10].lower():
                            # 跳过 ```json
                            start_pos = clean_rsp.find("\n", start_pos) + 1
                        else:
                            # 跳过 ```
                            start_pos = start_pos + 3
                            # 如果下一行是language标记，再跳过一行
                            if clean_rsp[start_pos:].strip().startswith("json"):
                                start_pos = clean_rsp.find("\n", start_pos) + 1
                        
                        # 找到最后一个 ```
                        end_pos = clean_rsp.rfind("```")
                        if end_pos > start_pos:
                            clean_rsp = clean_rsp[start_pos:end_pos].strip()
                            logger.info("从markdown代码块中提取了JSON内容")
                        else:
                            # 可能只有开始的```但没有结束的```
                            clean_rsp = clean_rsp[start_pos:].strip()
                            logger.info("从不完整markdown代码块中提取了JSON内容")
                    
                    # 2. 查找JSON的实际开始位置（找到第一个 { 字符）
                    json_start = clean_rsp.find("{")
                    if json_start != -1:
                        # 检查开始之前是否有非JSON内容
                        if json_start > 0:
                            pre_content = clean_rsp[:json_start].strip()
                            if pre_content:
                                logger.info(f"删除JSON前的内容: {pre_content[:50]}...")
                            clean_rsp = clean_rsp[json_start:]
                    
                        # 3. 查找JSON的实际结束位置（找到最后一个 } 字符）
                        json_end = clean_rsp.rfind("}")
                        if json_end != -1 and json_end < len(clean_rsp) - 1:
                            post_content = clean_rsp[json_end+1:].strip()
                            if post_content:
                                logger.info(f"删除JSON后的内容: {post_content[:50]}...")
                            clean_rsp = clean_rsp[:json_end+1]
                    
                    # 4. 检查并移除占位符
                    if "..." in clean_rsp:
                        logger.warning("检测到JSON中包含占位符'...'，尝试修复")
                        # 检查是否在数组中
                        
                        # 标准数组模式：["item1", "item2", "..."]，替换为["item1", "item2"]
                        clean_rsp = re.sub(r'(\[(?:"[^"]*",\s*)*)"[^"]*\.\.\.(?:[^"]*)"(,?\s*\])', r'\1\2', clean_rsp)
                        
                        # 完全空的数组模式：["..."]，替换为[]
                        clean_rsp = re.sub(r'\[\s*"\.\.\."\s*\]', '[]', clean_rsp)
                        
                        # 单项数组中的省略号：["item1 等..."]，替换为["item1"]
                        clean_rsp = re.sub(r'"([^"]*)等\.\.\.(?:[^"]*)"', r'"\1"', clean_rsp)
                        
                        # 如果还有其他占位符，替换整个字段
                        if "..." in clean_rsp:
                            logger.warning("尝试替换所有包含占位符的字段为空数组或不存在")
                            # 把带...的字符串替换为 "不存在"
                            clean_rsp = re.sub(r'"[^"]*\.\.\.(?:[^"]*)"', '"不存在"', clean_rsp)
                            # 把带...的数组替换为空数组
                            clean_rsp = re.sub(r'\[\s*(?:"[^"]*",\s*)?"[^"]*\.\.\.(?:[^"]*)"\s*(?:,\s*"[^"]*")?\s*\]', '[]', clean_rsp)
                    
                    # 5. 尝试解析JSON
                    try:
                        parsed_json = json.loads(clean_rsp)
                        
                        # 6. 检查每个字段，确保没有占位符
                        for key, value in parsed_json.items():
                            if isinstance(value, str) and ("..." in value or "等" in value):
                                parsed_json[key] = "不存在"
                            elif isinstance(value, list):
                                # 检查每个列表项
                                cleaned_list = []
                                has_placeholder = False
                                for item in value:
                                    if isinstance(item, str) and ("..." in item or "等" in item):
                                        has_placeholder = True
                                    else:
                                        cleaned_list.append(item)
                                
                                if has_placeholder:
                                    # 如果找到了占位符但还有其他有效项，使用清理后的列表
                                    if cleaned_list:
                                        parsed_json[key] = cleaned_list
                                    else:
                                        # 如果所有项都有问题，设为"不存在"
                                        parsed_json[key] = "不存在"
                        
                        # 成功解析并清理JSON
                        return json.dumps(parsed_json, ensure_ascii=False)
                    
                    except json.JSONDecodeError as je:
                        logger.error(f"JSON解析失败: {je}, 位置: {je.pos}, 行: {je.lineno}, 列: {je.colno}")
                        logger.error(f"问题文本: {clean_rsp[max(0, je.pos-30):min(len(clean_rsp), je.pos+30)]}")
                        
                        # 如果是最后一次尝试，则执行更深层次的修复
                        if attempt == max_attempts - 1:
                            try:
                                # 1. 修复常见的JSON语法问题
                                fixed_json = clean_rsp
                                # 替换单引号为双引号
                                fixed_json = fixed_json.replace("'", '"')
                                # 替换JS风格注释
                                fixed_json = re.sub(r'//.*?\n', '\n', fixed_json)
                                fixed_json = re.sub(r'/\*.*?\*/', '', fixed_json, flags=re.DOTALL)
                                
                                # 移除占位符 "..."
                                fixed_json = fixed_json.replace('"..."', '"不存在"')
                                fixed_json = re.sub(r'\[\s*"\.\.\."\s*\]', '[]', fixed_json)
                                # 移除尾部可能残留的逗号
                                fixed_json = re.sub(r',\s*}', '}', fixed_json)
                                fixed_json = re.sub(r',\s*\]', ']', fixed_json)
                                
                                # 2. 处理可能的Unicode问题
                                try:
                                    # 检查BOM标记
                                    if fixed_json.startswith('\ufeff'):
                                        fixed_json = fixed_json[1:]
                                        logger.info("移除了BOM标记")
                                except:
                                    pass
                                
                                # 3. 尝试解析修复后的JSON
                                try:
                                    parsed_json = json.loads(fixed_json)
                                    logger.info("成功修复并解析JSON")
                                    return json.dumps(parsed_json, ensure_ascii=False)
                                except json.JSONDecodeError:
                                    # 如果还是失败，尝试更极端的方法
                                    logger.warning("常规修复失败，尝试更极端的JSON修复方法")
                                    
                                    # 尝试用正则表达式提取可能的JSON部分
                                    # 寻找可能的完整JSON对象
                                    json_pattern = r'(\{.*\})'
                                    matches = re.search(json_pattern, fixed_json, re.DOTALL)
                                    if matches:
                                        potential_json = matches.group(1)
                                        try:
                                            parsed_json = json.loads(potential_json)
                                            logger.info("通过正则表达式提取并成功解析JSON")
                                            return json.dumps(parsed_json, ensure_ascii=False)
                                        except:
                                            pass
                                    
                                    # 最后手段：构建一个基本的JSON对象
                                    return self._create_fallback_json(document_content)
                            except Exception as repair_err:
                                logger.error(f"尝试修复JSON失败: {str(repair_err)}")
                                # 返回一个基本的JSON对象
                                return self._create_fallback_json(document_content)
                        else:
                            logger.warning(f"第 {attempt+1} 次解析失败，将重试")
                
                except Exception as e:
                    logger.error(f"解析文档过程中发生错误: {str(e)}")
                    if attempt == max_attempts - 1:
                        import traceback
                        logger.error(f"异常详情: {traceback.format_exc()}")
                        # 返回一个基本的JSON对象
                        return self._create_fallback_json(document_content)
                    else:
                        logger.warning(f"第 {attempt+1} 次解析出错，将重试")
            
            # 如果所有尝试都失败
            return self._create_fallback_json(document_content)
            
        except Exception as e:
            logger.error(f"解析文档过程中发生未预期错误: {str(e)}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            # 返回一个基本的JSON对象
            return self._create_fallback_json(document_content)
    
    def _create_fallback_json(self, document_content: str) -> str:
        """创建一个基本的JSON对象，当所有解析尝试都失败时使用
        
        Args:
            document_content: 原始文档内容
            
        Returns:
            str: 基本的JSON字符串
        """
        logger.warning("创建基本的JSON结构作为备选")
        
        # 提取文档的前200个字符作为文档结构描述
        doc_preview = document_content[:200] + ("..." if len(document_content) > 200 else "")
        
        fallback_json = {
            "document_structure": f"文档解析失败，内容开头为: {doc_preview}",
            "functional_requirements": "解析失败",
            "system_interfaces": "解析失败",
            "deadlines": "解析失败",
            "developer_information": "解析失败"
        }
        
        return json.dumps(fallback_json, ensure_ascii=False)


class CheckDocumentPoint(Action):
    """动作：检查专家检查文档是否符合特定检查点的要求"""

    PROMPT_TEMPLATE: str = """
    ## 背景介绍
    你是文档检查专家，需要根据特定检查点评估解析后的需求文档。

    ## 解析后的文档
    {parsed_document}

    ## 原始文档
    {original_document}

    ## 检查点
    {check_point}

    ## 检查标准
    {check_point_definition}

    ## 检查指南
    在评估文档是否满足检查点要求时，请严格遵循以下标准进行评估：
    
    1. 满足：文档完全符合检查点要求，包含所有必要信息且格式正确。
       - 参考检查标准中"satisfied"部分的标准和示例。
       - 示例：如果检查点是"是否包含具体的截止时间"，则文档中必须明确包含具体日期时间（如"2025年4月10日前"）。
    
    2. 部分满足：文档包含了相关信息，但不完整或不够明确。
       - 参考检查标准中"partially_satisfied"部分的标准和示例。
       - 示例：文档提到了时间范围但没有具体日期，或者只在某些地方提到了时间要求。
    
    3. 不满足：文档完全没有包含相关信息，或者所包含的信息与检查点要求完全不符。
       - 参考检查标准中"not_satisfied"部分的标准和示例。
       - 示例：文档中完全没有提到任何时间相关信息。
    
    ## 极其重要的反幻觉指导 - 必须严格遵守
    - 你的评估必须100%基于文档的实际内容，绝对不允许虚构、编造或扩展不存在的内容。
    - 当原始文档完全没有提到某项内容时，评估结果必须是"不满足"，不能是"部分满足"或"满足"。
    - 每个评估必须引用文档中的确切原文作为证据，引用必须是实际存在的文本，不得改写或扩展。
    - 在评估证据时，必须使用引号标注从文档中直接复制的文本，例如："2025年4月10日前完成"。
    - 引用的文本必须在原始文档中通过搜索能找到，不得有任何字词的更改或替换。
    - 不得将检查标准中的示例误认为是被检查文档中的内容，示例仅用于解释评估标准。
    - 如果你无法在文档中找到与检查点相关的内容，必须评估为"不满足"，并明确说明文档中不包含此类信息。
    - 在给出改进建议时，必须与评估结果一致，不要建议修改已经存在的内容。
    - 对于没有在文档中发现的信息，不要在解释中表示"文档提到了X但没有明确Y"，而应直接说"文档中没有提到X"。
    - 检查点定义和示例不是文档的一部分，不要引用它们作为文档内容。

    ## 检查任务
    请严格按照以下步骤和格式评估文档：

    1. 首先仔细通读原始文档内容，确保你理解文档的全部内容，并能准确判断内容是否存在。
    2. 对照检查点，明确在文档中搜索相关内容，不要假设或想象不存在的内容。
    3. 根据检查标准中的标准进行评估，仅使用文档中的确切文字作为证据。
    4. 给出符合以下格式的评估结果：

    检查点：{check_point}
    评估结果：[满足/不满足/部分满足]
    详细说明：[说明文档是否满足该检查点，以及满足或不满足的具体证据。必须引用文档中的实际内容，使用引号标明直接引用。如果文档中没有相关内容，明确说明"文档中没有提到..."。不得编造不存在的内容。]
    改进建议：[如何改进文档以更好地满足该检查点的要求。建议必须与评估结果一致。]
    """
    name: str = "CheckDocumentPoint"

    async def run(self, parsed_document: str, original_document: str, check_point: str):
        """执行文档检查

        Args:
            parsed_document: 解析后的文档结构（JSON格式字符串）
            original_document: 原始文档内容
            check_point: 检查点描述

        Returns:
            str: 检查结果，包含评估和详细说明
        """
        # 提取检查点定义
        check_point_definition = ""
        try:
            # 尝试从parsed_document中提取check_point_definition
            parsed_json = json.loads(parsed_document)
            if "check_point_definition" in parsed_json:
                check_point_definition_obj = parsed_json.pop("check_point_definition", {})
                
                # 有定义时，构建详细定义信息
                if check_point_definition_obj:
                    check_point_definition = f"""根据检查标准定义:
                    
【名称】{check_point_definition_obj.get('name', check_point)}
【描述】{check_point_definition_obj.get('description', '检查文档是否满足相关要求')}

【满足标准】
- 标准: {check_point_definition_obj.get('satisfied', {}).get('标准', '文档完全满足该检查点要求')}
- 示例: {check_point_definition_obj.get('satisfied', {}).get('示例', '标准示例不可用')}

【部分满足标准】
- 标准: {check_point_definition_obj.get('partially_satisfied', {}).get('标准', '文档部分满足该检查点要求')}
- 示例: {check_point_definition_obj.get('partially_satisfied', {}).get('示例', '标准示例不可用')}

【不满足标准】
- 标准: {check_point_definition_obj.get('not_satisfied', {}).get('标准', '文档不满足该检查点要求')}
- 示例: {check_point_definition_obj.get('not_satisfied', {}).get('示例', '标准示例不可用')}
"""
                    logger.info(f"使用定义检查点: {check_point}")
                else:
                    check_point_definition = "没有提供检查点标准定义，请根据常识和一般要求进行评估。"
                    logger.warning(f"未找到检查点 '{check_point}' 的定义")
                
                # 移除定义信息后重新序列化
                parsed_document = json.dumps(parsed_json, ensure_ascii=False)
        except Exception as e:
            logger.error(f"提取检查点定义时出错: {e}")
            check_point_definition = "提取检查点定义时出错，请根据常识和一般要求进行评估。"
        
        prompt = self.PROMPT_TEMPLATE.format(
            parsed_document=parsed_document,
            original_document=original_document,
            check_point=check_point,
            check_point_definition=check_point_definition
        )
        
        logger.info(f"开始检查点 '{check_point}' 的评估")
        
        try:
            # 尝试多次评估，如果失败则重试
            max_attempts = 2
            for attempt in range(max_attempts):
                try:
                    # 请求AI进行评估
                    rsp = await self._aask(prompt)
                    logger.info(f"完成对检查点 '{check_point}' 的评估")
                    return rsp
                except Exception as e:
                    if attempt < max_attempts - 1:
                        logger.warning(f"评估检查点 '{check_point}' 时出错，尝试重试: {e}")
                    else:
                        logger.error(f"评估检查点 '{check_point}' 最终失败: {e}")
                        return f"""
                        检查点：{check_point}
                        评估结果：评估失败
                        详细说明：在评估过程中发生错误：{str(e)}
                        改进建议：请手动检查该检查点。
                        """
        except Exception as e:
            logger.error(f"评估检查点 '{check_point}' 过程中发生未捕获的错误: {e}")
            return f"""
            检查点：{check_point}
            评估结果：评估失败
            详细说明：在评估过程中发生意外错误：{str(e)}
            改进建议：请手动检查该检查点。
            """


class SummarizeDocument(Action):
    """动作：文档总结专家汇总检查结果"""

    PROMPT_TEMPLATE: str = """
    ## 背景介绍
    你是文档总结专家，需要汇总所有检查点的评估结果，生成最终报告。

    ## 检查结果
    {check_results}

    ## 总结任务
    请根据以上检查结果，生成一份综合评估报告。报告应包含以下内容：
    1. 总体评估概述
    2. 已满足的检查点列表
    3. 未满足的检查点列表
    4. 部分满足的检查点列表
    5. 具体的改进建议

    请使用以下格式回答：

    # 需求文档检查报告

    ## 总体评估
    [总体评估内容]

    ## 满足的检查点
    - [检查点1]
    - [检查点2]
    ...

    ## 未满足的检查点
    - [检查点1]：[简短说明为什么未满足]
    - [检查点2]：[简短说明为什么未满足]
    ...

    ## 部分满足的检查点
    - [检查点1]：[简短说明为什么部分满足]
    - [检查点2]：[简短说明为什么部分满足]
    ...

    ## 改进建议
    1. [建议1]
    2. [建议2]
    ...
    """
    name: str = "SummarizeDocument"

    async def run(self, check_results: List[str]):
        """执行文档总结

        Args:
            check_results: 所有检查点的检查结果列表

        Returns:
            str: 综合评估报告
        """
        # 将检查结果列表转换为字符串
        check_results_str = "\n\n".join(check_results)
        prompt = self.PROMPT_TEMPLATE.format(check_results=check_results_str)
        rsp = await self._aask(prompt)
        return rsp 