"""
文件名: MetaGPT/examples/a_ymg_learn/documents_analysis/modules/actions.py
创建日期: 2024年
描述: 文档检查系统中的所有Action类实现
"""

import json
import logging
import re
import os
from typing import Dict, List, ClassVar

from metagpt.actions import Action
from metagpt.logs import logger

class ParseDocument(Action):
    """动作：文档解析专家解析文档"""

    # 基础提示模板，将在run方法中根据check_points_definition.json动态扩展
    PROMPT_TEMPLATE: str = """
    ## 背景介绍
    你是文档解析专家，需要解析用户提供的需求文档。

    ## 用户文档
    {document_content}

    ## 文档解析任务
    请详细分析上述文档，提取以下内容：
    1. 文档的主要结构和组织方式
    {extraction_fields}

    ## 极其重要的解析准则
    - 只提取文档中明确存在的内容，不要进行过度推断或填充不存在的信息
    - 如果文档中某项内容不存在，请明确标注为"不存在"，不要使用空数组或空对象
    - 同一内容不应重复出现在多个字段中，确保信息分类准确
    - 每个字段都应严格按照其定义提取信息，不要为了填充结构而强行解释不相关内容
    - 如果无法确定某项内容属于哪个类别，宁可将其标记为"不存在"也不要错误分类
    - 对于日期和时间信息，请特别注意准确提取，例如"2025年4月10日前完成"这样的表述

    特别提示：
    - 如果文档中某项内容真的不存在，请直接使用字符串"不存在"而非空数组[]或空对象{{}}
    - 返回的JSON必须是有效的格式，不要包含注释或其他非JSON元素
    - 不要在JSON中使用占位符（如"..."、"等"）
    - 不要为了满足格式而捏造不存在的内容

    请以JSON格式返回解析结果，保证格式的有效性，不要包含任何非JSON内容。不要使用markdown代码块，直接返回纯JSON字符串。

    示例输出格式（注意这只是格式示例，实际内容应根据你的解析结果填充）：
    {json_start}
      "document_structure": "文档分为X个部分，包括引言、功能需求等...",
      {example_fields}
    {json_end}

    如果某些内容在文档中不存在，请这样标注：
    {json_start}
      "document_structure": "文档分为简介和系统功能两部分",
      {empty_fields}
    {json_end}

    重要提示：只返回纯JSON格式，不要有任何额外说明，也不要使用```json和```这样的标记。请确保你的JSON是有效的，所有字符串都用双引号，不要使用任何占位符或省略号。
    """
    name: str = "ParseDocument"
    
    # 检查点ID到解析字段的映射
    CHECKPOINT_TO_FIELD_MAP: ClassVar[Dict[str, Dict[str, str]]] = {
        "functionality_requirements": {
            "field": "functional_requirements",
            "description": "文档中描述的功能需求（详细列出每一条）",
            "example": '"functional_requirements": ["系统应提供用户登录功能", "系统应支持数据导出功能"]'
        },
        "interface_definitions": {
            "field": "system_interfaces",
            "description": "文档中定义的系统接口（如API、调用方式等）",
            "example": '"system_interfaces": ["RESTful API接口: POST /api/login, 参数: username, password", "数据库接口: MySQL连接"]'
        },
        "deadline_specifications": {
            "field": "deadlines",
            "description": "文档中提到的截止时间（如日期、期限等）",
            "example": '"deadlines": ["2025年4月10日前完成系统开发"]'
        },
        "developer_information": {
            "field": "developer_information",
            "description": "文档中包含的开发人员信息（如姓名、角色、联系方式等）",
            "example": '"developer_information": ["项目经理: 张三 (电话: 123456789)", "开发工程师: 李四 (邮箱: <EMAIL>)"]'
        }
    }

    # 检查点列表
    check_points_list: List[str] = []

    def _build_dynamic_prompt(self, document_content: str, local_check_points: List[str] = None) -> str:
        """根据检查点列表动态构建提示模板
        
        Args:
            document_content: 文档内容
            local_check_points: 检查点列表，如果提供则优先使用
            
        Returns:
            str: 构建好的提示模板
        """
        # 1. 构建要提取的字段描述
        extraction_fields = []
        example_fields = []
        empty_fields = []
        field_index = 2  # 从2开始，因为文档结构已经是第1项
        
        # 使用传入的检查点列表或类变量
        check_points_to_use = local_check_points if local_check_points is not None else self.check_points_list
        
        # 每个ID到字段的映射
        id_to_field = {}
        name_to_id = {}
        
        # 尝试从JSON定义文件中加载检查点ID映射
        try:
            # 查找当前目录及上级目录中的check_points_definition.json
            possible_paths = [
                "check_points_definition.json",
                "../check_points_definition.json",
                "../../check_points_definition.json",
                "examples/a_ymg_learn/documents_analysis/check_points_definition.json"
            ]
            
            def_file_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    def_file_path = path
                    break
            
            if def_file_path:
                logger.info(f"尝试从 {def_file_path} 加载检查点定义")
                from .utils import read_json_file
                check_points_defs, success = read_json_file(def_file_path)
                
                if success and check_points_defs and isinstance(check_points_defs, list):
                    # 构建检查点名称到ID的映射
                    for def_item in check_points_defs:
                        if isinstance(def_item, dict) and 'id' in def_item and 'name' in def_item:
                            name_to_id[def_item['name']] = def_item['id']
                            logger.info(f"映射检查点 '{def_item['name']}' 到ID '{def_item['id']}'")
        except Exception as e:
            logger.warning(f"加载检查点定义时出错: {e}")
        
        # 对于检查点列表中的每个检查点，添加相应的字段
        logger.info(f"使用检查点列表构建提取字段，共 {len(check_points_to_use)} 个检查点")
        for check_point in check_points_to_use:
            # 尝试找到检查点对应的ID
            checkpoint_id = name_to_id.get(check_point)
            field_info = None
            
            if checkpoint_id and checkpoint_id in self.CHECKPOINT_TO_FIELD_MAP:
                field_info = self.CHECKPOINT_TO_FIELD_MAP[checkpoint_id]
                logger.info(f"检查点 '{check_point}' (ID: {checkpoint_id}) 映射到字段 '{field_info['field']}'")
            else:
                # 尝试通过关键词匹配
                for id_key, field_data in self.CHECKPOINT_TO_FIELD_MAP.items():
                    key_words = id_key.split('_')
                    if any(word.lower() in check_point.lower() for word in key_words if len(word) > 3):
                        field_info = field_data
                        logger.info(f"关键词匹配：检查点 '{check_point}' 映射到字段 '{field_data['field']}'")
                        break
            
            if field_info:
                # 添加字段到提取列表
                extraction_fields.append(f"{field_index}. {field_info['description']}")
                example_fields.append(f'"{field_info["field"]}": {field_info["example"]}')
                empty_fields.append(f'"{field_info["field"]}": "不存在"')
                id_to_field[field_info["field"]] = True
                field_index += 1
            else:
                logger.warning(f"无法为检查点 '{check_point}' 找到对应的字段映射")
        
        # 如果没有找到任何有效的字段映射，使用默认字段
        if not id_to_field:
            logger.warning("没有找到任何有效的字段映射，使用基本字段")
            extraction_fields.append(f"{field_index}. 文档中明确提到的功能需求（如果有）")
            example_fields.append('"functional_requirements": ["系统应提供用户登录功能", "系统应支持数据导出功能"]')
            empty_fields.append('"functional_requirements": "不存在"')
            field_index += 1
            
            extraction_fields.append(f"{field_index}. 文档中定义的系统接口（如果有）")
            example_fields.append('"system_interfaces": ["RESTful API接口: POST /api/login, 参数: username, password", "数据库接口: MySQL连接"]')
            empty_fields.append('"system_interfaces": "不存在"')
            field_index += 1
            
            extraction_fields.append(f"{field_index}. 文档中提到的截止时间（如果有）")
            example_fields.append('"deadlines": ["2025年4月10日前完成系统开发"]')
            empty_fields.append('"deadlines": "不存在"')
            field_index += 1
        
        # 构建最终的提示模板
        try:
            # 检查模板中的占位符是否都提供了对应的值
            expected_placeholders = [
                "document_content", "extraction_fields", 
                "example_fields", "empty_fields", 
                "json_start", "json_end"
            ]
            for placeholder in expected_placeholders:
                if "{" + placeholder + "}" in self.PROMPT_TEMPLATE and placeholder not in {
                    "document_content": document_content,
                    "extraction_fields": "\n".join(extraction_fields),
                    "example_fields": ",\n      ".join(example_fields),
                    "empty_fields": ",\n      ".join(empty_fields),
                    "json_start": "{",
                    "json_end": "}"
                }:
                    logger.warning(f"提示模板中的占位符 '{placeholder}' 没有对应的值")
            
            # 使用命名参数进行格式化，确保所有占位符都有对应的值
            prompt = self.PROMPT_TEMPLATE.format(
                document_content=document_content,
                extraction_fields="\n".join(extraction_fields),
                example_fields=",\n      ".join(example_fields),
                empty_fields=",\n      ".join(empty_fields),
                json_start="{",
                json_end="}"
            )
            
            # 记录调试信息
            logger.debug(f"提示模板格式化成功，长度: {len(prompt)} 字符")
            return prompt
            
        except KeyError as e:
            # 处理缺少关键字的情况
            logger.error(f"格式化提示模板时缺少关键字: {e}")
            # 创建一个简单的后备提示
            return self._create_fallback_prompt(document_content)
        except IndexError as e:
            # 处理索引错误的情况
            logger.error(f"格式化提示模板时出现索引错误: {e}")
            # 打印更详细的诊断信息
            logger.error(f"模板格式化诊断: 提供了以下参数:")
            logger.error(f"- document_content: {'有值' if document_content else '无值'}")
            logger.error(f"- extraction_fields: {len(extraction_fields)} 项")
            logger.error(f"- example_fields: {len(example_fields)} 项")
            logger.error(f"- empty_fields: {len(empty_fields)} 项")
            return self._create_fallback_prompt(document_content)
        except Exception as e:
            # 处理其他异常
            logger.error(f"格式化提示模板时出现未预期的错误: {e}")
            return self._create_fallback_prompt(document_content)

    def _create_fallback_prompt(self, document_content: str) -> str:
        """创建一个简单的后备提示模板，用于格式化失败的情况
        
        Args:
            document_content: 文档内容
            
        Returns:
            str: 简单的提示模板
        """
        logger.warning("使用后备提示模板")
        return f"""
        ## 背景介绍
        你是文档解析专家，需要解析用户提供的需求文档。

        ## 用户文档
        {document_content}

        ## 文档解析任务
        请详细分析上述文档，提取以下内容：
        1. 文档的主要结构和组织方式
        2. 文档中描述的功能需求（详细列出每一条）
        3. 文档中定义的系统接口（如API、调用方式等）
        4. 文档中提到的截止时间（如日期、期限等）

        ## 极其重要的解析准则
        - 只提取文档中明确存在的内容，不要进行过度推断或填充不存在的信息
        - 如果文档中某项内容不存在，请明确标注为"不存在"，不要使用空数组或空对象

        示例输出格式：
        {{{{
          "document_structure": "文档结构描述",
          "functional_requirements": ["需求1", "需求2"],
          "system_interfaces": ["接口1", "接口2"],
          "deadlines": ["2025年4月10日前完成"]
        }}}}

        如果某内容不存在，请这样标注：
        {{{{
          "document_structure": "文档结构描述",
          "functional_requirements": ["需求1"],
          "system_interfaces": "不存在",
          "deadlines": ["截止时间"]
        }}}}

        请以JSON格式返回解析结果，不要包含任何非JSON内容。
        """

    async def run(self, document_content: str, check_points: List[str] = None):
        """执行文档解析

        Args:
            document_content: 需求文档内容
            check_points: 检查点列表，可选，如果提供则优先使用

        Returns:
            str: 解析后的文档结构（JSON格式字符串）
        """
        try:
            # 使用传入的检查点列表（如果有）
            if check_points:
                local_check_points = check_points
            else:
                local_check_points = self.check_points_list
                
            # 根据检查点动态构建提示模板
            prompt = self._build_dynamic_prompt(document_content, local_check_points)
            logger.info(f"文档解析提示构建完成，长度: {len(prompt)} 字符")
            
            try:
                # 尝试多次解析，如果失败则重试
                max_attempts = 2
                for attempt in range(max_attempts):
                    try:
                        # 请求AI解析文档
                        rsp = await self._aask(prompt)
                        
                        # 记录返回的前100个字符，用于调试
                        logger.info(f"AI返回结果前100个字符: {rsp[:100] if rsp else '空'}")
                        
                        # 处理返回结果，确保是有效的JSON
                        if not rsp or rsp.strip() == "":
                            logger.warning("AI返回了空结果，使用空JSON对象")
                            return "{}"
                        
                        # 预处理：清理常见的非JSON内容
                        clean_rsp = rsp.strip()
                        
                        # 1. 移除Markdown代码块标记
                        # 处理 ```json 或 ``` 开头的情况
                        if clean_rsp.startswith("```"):
                            # 找到第一个和最后一个 ``` 标记
                            start_pos = clean_rsp.find("```")
                            # 检查是否有语言标识
                            if "json" in clean_rsp[start_pos:start_pos+10].lower():
                                # 跳过 ```json
                                start_pos = clean_rsp.find("\n", start_pos) + 1
                            else:
                                # 跳过 ```
                                start_pos = start_pos + 3
                                # 如果下一行是language标记，再跳过一行
                                if clean_rsp[start_pos:].strip().startswith("json"):
                                    start_pos = clean_rsp.find("\n", start_pos) + 1
                            
                            # 找到最后一个 ```
                            end_pos = clean_rsp.rfind("```")
                            if end_pos > start_pos:
                                clean_rsp = clean_rsp[start_pos:end_pos].strip()
                                logger.info("从markdown代码块中提取了JSON内容")
                            else:
                                # 可能只有开始的```但没有结束的```
                                clean_rsp = clean_rsp[start_pos:].strip()
                                logger.info("从不完整markdown代码块中提取了JSON内容")
                        
                        # 2. 查找JSON的实际开始位置（找到第一个 { 字符）
                        json_start = clean_rsp.find("{")
                        if json_start != -1:
                            # 检查开始之前是否有非JSON内容
                            if json_start > 0:
                                pre_content = clean_rsp[:json_start].strip()
                                if pre_content:
                                    logger.info(f"删除JSON前的内容: {pre_content[:50]}...")
                                clean_rsp = clean_rsp[json_start:]
                        
                            # 3. 查找JSON的实际结束位置（找到最后一个 } 字符）
                            json_end = clean_rsp.rfind("}")
                            if json_end != -1 and json_end < len(clean_rsp) - 1:
                                post_content = clean_rsp[json_end+1:].strip()
                                if post_content:
                                    logger.info(f"删除JSON后的内容: {post_content[:50]}...")
                                clean_rsp = clean_rsp[:json_end+1]
                                
                        # 对处理后的JSON进行内容优化
                        clean_rsp = self._standardize_field_formats(self._deduplicate_content(clean_rsp))
                        
                        # 验证JSON是否有效
                        try:
                            json.loads(clean_rsp)
                            logger.info("成功解析为有效的JSON")
                            return clean_rsp
                        except json.JSONDecodeError as je:
                            if attempt == max_attempts - 1:
                                logger.warning(f"所有尝试都失败，最后一次错误: {je}")
                                # 创建一个简单的替代JSON
                                return self._create_fallback_json(document_content)
                            else:
                                logger.warning(f"第 {attempt + 1} 次尝试解析JSON失败: {je}，重试...")
                    
                    except Exception as e:
                        if attempt == max_attempts - 1:
                            logger.error(f"处理AI响应时出错: {e}")
                            # 创建一个简单的替代JSON
                            return self._create_fallback_json(document_content)
                        else:
                            logger.warning(f"第 {attempt + 1} 次尝试处理AI响应时出错: {e}，重试...")
                
                # 如果执行到这里，说明所有尝试都失败
                logger.error("所有解析尝试都失败")
                return self._create_fallback_json(document_content)
                
            except Exception as e:
                logger.error(f"请求AI解析文档时出错: {e}")
                return self._create_fallback_json(document_content)
                
        except Exception as e:
            logger.error(f"文档解析过程中发生严重错误: {e}")
            # 返回一个最基本的空JSON
            return '{"document_structure": "解析出错", "error": "文档解析过程中发生错误"}'
    
    def _create_fallback_json(self, document_content: str) -> str:
        """创建一个基本的JSON对象，当所有解析尝试都失败时使用
        
        Args:
            document_content: 原始文档内容
            
        Returns:
            str: 基本的JSON字符串
        """
        logger.warning("创建基本的JSON结构作为备选")
        
        # 提取文档的前200个字符作为文档结构描述
        doc_preview = document_content[:200] + ("..." if len(document_content) > 200 else "")
        
        fallback_json = {
            "document_structure": f"文档解析失败，内容开头为: {doc_preview}",
            "functional_requirements": "解析失败",
            "system_interfaces": "解析失败",
            "deadlines": "解析失败",
            "developer_information": "解析失败"
        }
        
        return json.dumps(fallback_json, ensure_ascii=False)

    def _deduplicate_content(self, json_str: str) -> str:
        """去除JSON中的重复内容
        
        Args:
            json_str: JSON格式的字符串
            
        Returns:
            str: 去除重复内容后的JSON字符串
        """
        try:
            # 尝试解析JSON
            parsed_json = json.loads(json_str)
            
            # 检查每个字段，确保没有占位符，防止信息重复出现在多个类别中
            content_tracker = {}  # 用于追踪内容是否已在其他字段中出现
            duplicate_content = {}  # 记录重复内容
            
            for key, value in parsed_json.items():
                if isinstance(value, str) and ("..." in value or "等" in value):
                    parsed_json[key] = "不存在"
                elif isinstance(value, list):
                    # 检查每个列表项是否有占位符或已在其他字段出现
                    cleaned_list = []
                    has_placeholder = False
                    
                    for item in value:
                        # 检查是否有占位符
                        if isinstance(item, str):
                            if "..." in item or "等" in item:
                                has_placeholder = True
                                continue
                            
                            # 检查是否重复（与其他字段）
                            if item in content_tracker:
                                # 只有当项目不是日期且不是常见短语时才视为重复
                                if not any(date_pattern in item for date_pattern in ["年", "月", "日", "前完成", "截止"]) and len(item) > 10:
                                    duplicate_content[item] = [content_tracker[item], key]
                                    logger.warning(f"发现重复内容 '{item}'，出现在 {content_tracker[item]} 和 {key}")
                                    continue
                            else:
                                content_tracker[item] = key
                            
                            cleaned_list.append(item)
                        else:
                            cleaned_list.append(item)
                    
                    if has_placeholder:
                        # 如果找到了占位符但还有其他有效项，使用清理后的列表
                        if cleaned_list:
                            parsed_json[key] = cleaned_list
                        else:
                            # 如果所有项都有问题，设为"不存在"
                            parsed_json[key] = "不存在"
                    else:
                        parsed_json[key] = cleaned_list
                        # 如果列表为空，设为"不存在"
                        if not cleaned_list:
                            parsed_json[key] = "不存在"
            
            # 处理重复内容，保留最合适的分类
            if duplicate_content:
                for content, categories in duplicate_content.items():
                    original_category, duplicate_category = categories
                    
                    # 根据内容和类别判断最适合的分类
                    keep_in_original = self._is_better_fit(content, original_category, duplicate_category)
                    
                    if keep_in_original:
                        # 从重复类别中移除
                        if isinstance(parsed_json[duplicate_category], list):
                            parsed_json[duplicate_category] = [item for item in parsed_json[duplicate_category] if item != content]
                            if not parsed_json[duplicate_category]:
                                parsed_json[duplicate_category] = "不存在"
                    else:
                        # 从原始类别中移除
                        if isinstance(parsed_json[original_category], list):
                            parsed_json[original_category] = [item for item in parsed_json[original_category] if item != content]
                            if not parsed_json[original_category]:
                                parsed_json[original_category] = "不存在"
            
            # 返回处理后的JSON
            return json.dumps(parsed_json, ensure_ascii=False)
        except Exception as e:
            logger.error(f"去重过程中出错: {e}")
            return json_str

    def _is_better_fit(self, content: str, category1: str, category2: str) -> bool:
        """判断内容是否更适合分类到第一个类别中
        
        Args:
            content: 需要分类的内容
            category1: 第一个类别
            category2: 第二个类别
            
        Returns:
            bool: 如果更适合第一个类别则返回True，否则返回False
        """
        # 常见的类别关键词
        category_keywords = {
            "functional_requirements": ["需求", "功能", "应该", "必须", "能够", "提供"],
            "system_interfaces": ["接口", "API", "调用", "服务", "请求", "响应", "端口"],
            "deadlines": ["截止", "日期", "时间", "完成", "年", "月", "日", "之前", "之后"],
            "developer_information": ["开发", "人员", "团队", "联系", "电话", "邮箱", "经理"]
        }
        
        # 计算内容与每个类别的匹配度
        def calculate_match_score(text, category):
            score = 0
            keywords = category_keywords.get(category, [])
            for kw in keywords:
                if kw in text:
                    score += 1
            return score
        
        score1 = calculate_match_score(content, category1)
        score2 = calculate_match_score(content, category2)
        
        # 返回匹配度更高的类别
        return score1 >= score2  # 如果相等，保留在原始类别中

    def _standardize_field_formats(self, json_str_or_dict):
        """标准化字段格式，确保一致性
        
        Args:
            json_str_or_dict: JSON字符串或已解析的字典
            
        Returns:
            处理后的JSON字符串或修改后的字典（根据输入类型）
        """
        is_str_input = isinstance(json_str_or_dict, str)
        
        try:
            # 如果是字符串，解析为字典
            parsed_json = json.loads(json_str_or_dict) if is_str_input else json_str_or_dict
            
            # 标准化格式（确保列表应该是列表，单值应该是字符串）
            for key, value in parsed_json.items():
                # 标准化空值表示
                if value == [] or value == {} or value == "" or value is None:
                    parsed_json[key] = "不存在"
                # 标准化单个项目的列表表示
                elif key in ["functional_requirements", "system_interfaces", "deadlines", "developer_information"]:
                    if isinstance(value, str) and value != "不存在":
                        # 将非"不存在"的字符串转换为单项列表
                        parsed_json[key] = [value]
                    elif isinstance(value, list) and len(value) == 0:
                        parsed_json[key] = "不存在"
            
            # 返回字典或格式化的JSON字符串
            if is_str_input:
                return json.dumps(parsed_json, ensure_ascii=False)
            return parsed_json
            
        except Exception as e:
            logger.error(f"标准化字段格式时出错: {e}")
            # 返回原始输入
            return json_str_or_dict


class CheckDocumentPoint(Action):
    """动作：检查专家检查文档是否符合特定检查点的要求"""

    PROMPT_TEMPLATE: str = """
    ## 背景介绍
    你是文档检查专家，需要根据特定检查点评估解析后的需求文档。

    ## 解析后的文档
    {parsed_document}

    ## 原始文档
    {original_document}

    ## 检查点
    {check_point}

    ## 检查标准
    {check_point_definition}

    ## 检查指南
    在评估文档是否满足检查点要求时，请严格遵循以下标准进行评估：
    
    1. 满足：文档完全符合检查点要求，包含所有必要信息且格式正确。
       - 参考检查标准中"satisfied"部分的标准和示例。
       - 示例：如果检查点是"是否包含具体的截止时间"，则文档中必须明确包含具体日期时间（如"2025年4月10日前"）。
    
    2. 部分满足：文档包含了相关信息，但不完整或不够明确。
       - 参考检查标准中"partially_satisfied"部分的标准和示例。
       - 示例：文档提到了时间范围但没有具体日期，或者只在某些地方提到了时间要求。
    
    3. 不满足：文档完全没有包含相关信息，或者所包含的信息与检查点要求完全不符。
       - 参考检查标准中"not_satisfied"部分的标准和示例。
       - 示例：文档中完全没有提到任何时间相关信息。
    
    ## 极其重要的评估原则
    - 评估必须100%基于原始文档的实际内容，不要引用不存在的内容
    - 不要为了使文档通过检查点而强行解释或曲解文档内容
    - 如果某信息在文档中确实不存在，应直接评价为"不满足"，而不是尝试从有限的线索推断
    - 引用内容时，请使用引号标明原始文档中的确切文本，不得改写或扩展
    - 即使文档大体上符合主题，也要根据具体检查标准严格评估，而不是宽松处理
    - 评价必须与事实一致，如果不确定，选择更保守的评估结果
    - 不要将检查标准中的示例误认为是被检查文档中的内容，示例仅用于解释评估标准
    
    ## 检查任务
    请严格按照以下步骤和格式评估文档：

    1. 首先仔细阅读原始文档内容，确保你理解文档的全部内容，并能准确判断内容是否存在
    2. 对照检查点，明确在文档中搜索相关内容，不要假设或想象不存在的内容
    3. 根据检查标准进行评估，仅使用文档中的确切文字作为证据
    4. 给出符合以下格式的评估结果：

    检查点：{check_point}
    评估结果：[满足/不满足/部分满足]
    详细说明：[说明文档是否满足该检查点，以及满足或不满足的具体证据。必须引用文档中的实际内容，使用引号标明直接引用。如果文档中没有相关内容，明确说明"文档中没有提到..."。不得编造不存在的内容。]
    改进建议：[如何改进文档以更好地满足该检查点的要求。建议必须与评估结果一致，并且针对文档的实际不足之处。]
    """
    name: str = "CheckDocumentPoint"

    async def run(self, parsed_document: str, original_document: str, check_point: str, enhanced_prompt: str = None):
        """执行文档检查

        Args:
            parsed_document: 解析后的文档内容（JSON格式字符串）
            original_document: 原始文档内容
            check_point: 检查点内容
            enhanced_prompt: 可选的增强提示，通常包含幻觉检查者的反馈
            
        Returns:
            str: 检查结果
        """
        # 尝试提取检查点定义
        check_point_definition = ""
        try:
            # 如果parsed_document是JSON格式，尝试提取检查点定义
            parsed_data = json.loads(parsed_document)
            if "check_point_definition" in parsed_data:
                definition = parsed_data.pop("check_point_definition")
                # 将定义转换为文本格式
                lines = []
                for key, value in definition.items():
                    if key == "name" or key == "description":
                        lines.append(f"{key}: {value}")
                    elif isinstance(value, dict):
                        lines.append(f"{key}:")
                        for sub_key, sub_value in value.items():
                            if isinstance(sub_value, list):
                                lines.append(f"  {sub_key}:")
                                for item in sub_value:
                                    lines.append(f"    - {item}")
                            else:
                                lines.append(f"  {sub_key}: {sub_value}")
                check_point_definition = "\n".join(lines)
                parsed_document = json.dumps(parsed_data, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"提取检查点定义时出错: {e}")
            check_point_definition = "未找到检查点定义，请根据通用标准进行评估"
        
        # 构建基础提示
        base_prompt = self.PROMPT_TEMPLATE.format(
            parsed_document=parsed_document,
            original_document=original_document,
            check_point=check_point,
            check_point_definition=check_point_definition
        )
        
        # 如果有增强提示，添加到base_prompt前面
        if enhanced_prompt:
            prompt = enhanced_prompt + "\n\n" + base_prompt
            logger.info("使用增强提示进行检查")
        else:
            prompt = base_prompt
        
        logger.info(f"执行文档检查，检查点: {check_point}")
        
        # 执行检查
        try:
            rsp = await self._aask(prompt)
            
            # 验证返回结果格式
            if not rsp or "评估结果：" not in rsp:
                logger.warning(f"检查结果格式不正确: {rsp[:100] if rsp else '空'}")
                # 生成一个标准格式的默认响应
                rsp = f"""
                检查点：{check_point}
                评估结果：不满足
                详细说明：无法正确评估此检查点，可能是因为文档内容不足或检查标准不明确。
                改进建议：请提供更多相关信息以便进行准确评估。
                """
            
            return rsp.strip()
        except Exception as e:
            logger.error(f"执行文档检查时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 返回错误信息
            return f"""
            检查点：{check_point}
            评估结果：评估失败
            详细说明：执行检查过程中发生错误: {str(e)}
            改进建议：请检查系统日志并修复相关问题后重试。
            """


class SummarizeDocument(Action):
    """动作：文档总结专家汇总检查结果"""

    PROMPT_TEMPLATE: str = """
    ## 背景介绍
    你是文档总结专家，需要汇总所有检查点的评估结果，生成最终报告。

    ## 检查结果
    {check_results}

    ## 总结任务
    请根据以上检查结果，生成一份综合评估报告。报告应包含以下内容：
    1. 总体评估概述
    2. 已满足的检查点列表
    3. 未满足的检查点列表
    4. 部分满足的检查点列表
    5. 具体的改进建议

    请使用以下格式回答：

    # 需求文档检查报告

    ## 总体评估
    [总体评估内容]

    ## 满足的检查点
    - [检查点1]
    - [检查点2]
    ...

    ## 未满足的检查点
    - [检查点1]：[简短说明为什么未满足]
    - [检查点2]：[简短说明为什么未满足]
    ...

    ## 部分满足的检查点
    - [检查点1]：[简短说明为什么部分满足]
    - [检查点2]：[简短说明为什么部分满足]
    ...

    ## 改进建议
    1. [建议1]
    2. [建议2]
    ...
    """
    name: str = "SummarizeDocument"

    async def run(self, check_results: List[str]):
        """执行文档总结

        Args:
            check_results: 所有检查点的检查结果列表

        Returns:
            str: 综合评估报告
        """
        # 将检查结果列表转换为字符串
        check_results_str = "\n\n".join(check_results)
        prompt = self.PROMPT_TEMPLATE.format(check_results=check_results_str)
        rsp = await self._aask(prompt)
        return rsp 

class VerifyLogicalConsistency(Action):
    """动作：幻觉检查者验证文档、推理和结果的逻辑一致性"""
    
    name: str = "VerifyLogicalConsistency"
    
    PROMPT_TEMPLATE: str = """
    ## 背景介绍
    你是一位专业的幻觉检查专家，需要验证文档检查中的推理过程和结论是否存在幻觉或逻辑不一致。
    
    ## 原始文档
    {original_document}
    
    ## 检查点
    {check_point}
    
    ## 检查结果
    {check_result}
    
    ## 验证任务
    请仔细审核以上信息，验证检查结果中的推理是否与原始文档内容保持一致。具体需要验证以下几点：
    
    1. 检查结果中引用的原始文档内容是否确实存在于原始文档中
    2. 检查结果中的推理过程是否基于文档中实际存在的内容
    3. 检查结果中的结论是否与推理过程逻辑一致
    4. 是否存在没有根据的断言或假设
    5. 是否存在刻意匹配预设格式或过度解释的情况
    
    ## 验证指南
    - 仔细比对检查结果中引用的原文（通常用引号标注）是否与原始文档中的内容完全一致
    - 检查引用的上下文是否被正确理解和应用
    - 评估推理过程是否合理，结论是否符合逻辑
    - 特别关注是否存在"过度推断"，即基于有限信息做出过度延伸的结论
    - 注意检测是否存在为了满足特定结构而强行解释文档内容的情况
    - 识别检查结果中是否对同一内容进行了多重分类或重复引用
    - 对于模糊地带，检查评估是否选择了更保守的结果，而不是过于乐观的结果
    
    ## 关于过度匹配和强行解释的特别说明
    请特别关注以下情况：
    1. 将原始文档中的简单内容拆分为多个分类的情况
    2. 将截止日期信息放入功能需求等非时间类别的情况
    3. 将非功能性描述强行解释为功能需求的情况
    4. 将非接口描述强行解释为系统接口的情况
    5. 基于文档中相似或相关主题进行过度推断
    6. 为了让文档"通过"检查点而进行的曲解
    
    请以JSON格式返回验证结果，具体格式如下：
    
    ```
    {{
      "is_consistent": true/false,  // 检查结果是否与原始文档逻辑一致
      "hallucinations": [  // 发现的幻觉或不一致内容列表
        {{
          "quoted_text": "被引用的原文内容",  // 检查结果中声称引用的内容
          "issue": "描述问题所在，如：内容不存在、上下文错误、过度推断等",
          "correction": "如何修正该问题"
        }}
      ],
      "reasoning_issues": [  // 推理过程中的问题
        {{
          "statement": "有问题的推理陈述",
          "issue": "描述推理问题所在",
          "correction": "正确的推理方式"
        }}
      ],
      "format_matching_issues": [  // 刻意匹配格式的问题
        {{
          "content": "被不当分类的内容",
          "current_category": "当前分类",
          "issue": "描述分类问题",
          "correct_category": "正确的分类或处理方式"
        }}
      ],
      "conclusion_consistency": true/false,  // 结论是否与推理过程一致
      "overall_assessment": "总体评估，简要总结验证结果",
      "recommendation": "建议，如：需要重新评估/结果可信"
    }}
    ```
    
    请确保您的返回是有效的JSON格式。不要添加任何额外的评论或前缀。
    """
    
    async def run(self, original_document: str, check_point: str, check_result: str):
        """执行逻辑一致性验证
        
        Args:
            original_document: 原始文档内容
            check_point: 当前检查点
            check_result: 检查结果
            
        Returns:
            str: JSON格式的验证结果
        """
        prompt = self.PROMPT_TEMPLATE.format(
            original_document=original_document,
            check_point=check_point,
            check_result=check_result
        )
        
        logger.info(f"执行逻辑一致性验证，检查点: {check_point}")
        
        try:
            # 请求AI验证逻辑一致性
            rsp = await self._aask(prompt)
            
            # 记录返回的前100个字符，用于调试
            logger.info(f"逻辑一致性验证返回结果前100个字符: {rsp[:100] if rsp else '空'}")
            
            # 确保返回的是有效的JSON
            if not rsp or rsp.strip() == "":
                logger.warning("AI返回了空结果")
                return json.dumps({
                    "is_consistent": False,
                    "hallucinations": [],
                    "reasoning_issues": [],
                    "format_matching_issues": [],
                    "conclusion_consistency": False,
                    "overall_assessment": "验证过程发生错误，无法获取结果",
                    "recommendation": "需要重新评估"
                }, ensure_ascii=False)
            
            # 尝试解析JSON
            try:
                # 预处理：从返回中提取JSON部分
                # 处理可能包含的markdown代码块
                import re
                
                # 移除可能的markdown代码块标记
                if "```json" in rsp or "```" in rsp:
                    # 尝试提取代码块内容
                    matches = re.search(r'```(?:json)?(.*?)```', rsp, re.DOTALL)
                    if matches:
                        json_str = matches.group(1).strip()
                    else:
                        # 如果无法提取，尝试其他方式
                        json_str = rsp.replace("```json", "").replace("```", "").strip()
                else:
                    json_str = rsp.strip()
                
                # 解析JSON
                result = json.loads(json_str)
                
                # 确保format_matching_issues字段存在
                if "format_matching_issues" not in result:
                    result["format_matching_issues"] = []
                
                # 如果发现任何问题，设置is_consistent为False
                if (result.get("hallucinations") and len(result["hallucinations"]) > 0) or \
                   (result.get("reasoning_issues") and len(result["reasoning_issues"]) > 0) or \
                   (result.get("format_matching_issues") and len(result["format_matching_issues"]) > 0) or \
                   not result.get("conclusion_consistency", True):
                    result["is_consistent"] = False
                    if "需要重新评估" not in result.get("recommendation", ""):
                        result["recommendation"] = "需要重新评估" + \
                            (". " + result["recommendation"] if result.get("recommendation") else "")
                
                return json.dumps(result, ensure_ascii=False)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}")
                return json.dumps({
                    "is_consistent": False,
                    "hallucinations": [],
                    "reasoning_issues": [],
                    "format_matching_issues": [],
                    "conclusion_consistency": False,
                    "overall_assessment": f"返回结果不是有效的JSON格式: {str(e)}",
                    "recommendation": "需要重新评估"
                }, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"验证逻辑一致性时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return json.dumps({
                "is_consistent": False,
                "hallucinations": [],
                "reasoning_issues": [],
                "format_matching_issues": [],
                "conclusion_consistency": False,
                "overall_assessment": f"验证过程发生错误: {str(e)}",
                "recommendation": "需要重新评估"
            }, ensure_ascii=False) 