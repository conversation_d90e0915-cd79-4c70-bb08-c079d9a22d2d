# 文档分析系统技术文档

## 1. 概述

### 1.1 系统简介

文档分析系统是一个基于MetaGPT框架开发的智能文档检查工具，主要用于分析和评估需求文档的质量和完整性。系统通过定义的检查点对文档进行全面分析，生成评估报告，并提供具体的改进建议。

系统的主要功能包括：
- 对文档内容进行智能解析和结构化提取
- 基于预定义检查点评估文档质量
- 识别文档中的不足和缺失内容
- 生成详细的文档质量评估报告
- 提供具体的文档改进建议

### 1.2 应用场景

该系统适用于以下场景：
- 软件开发团队对需求文档进行质量检查
- 项目经理评估需求文档的完整性和准确性
- 文档撰写人员进行自检，提高文档质量
- 企业建立文档标准，确保文档质量一致性

就像菜市场的质检员会检查蔬菜水果的新鲜度和品质一样，文档分析系统会对文档内容进行全面检查，确保文档的"新鲜度"和"品质"。

## 2. 系统架构

### 2.1 整体架构

文档分析系统采用基于MetaGPT的多智能体协作架构，由三个核心角色组成：

1. **文档解析专家(DocumentParsingExpert)**：负责解析原始文档，提取关键信息和结构
2. **文档检查专家(DocumentCheckingExpert)**：根据特定检查点评估文档内容
3. **文档总结专家(DocumentSummaryExpert)**：汇总所有检查结果，生成综合评估报告

这三个角色通过消息传递进行协作，形成一个完整的工作流，类似于工厂中的流水线：原料进入、加工处理、质量检验、最终出品。

### 2.2 关键模块

系统由以下关键模块组成：

1. **核心角色(Roles)**：
   - DocumentParsingExpert - 文档解析专家
   - DocumentCheckingExpert - 文档检查专家
   - DocumentSummaryExpert - 文档总结专家

2. **智能动作(Actions)**：
   - ParseDocument - 文档解析动作
   - CheckDocumentPoint - 文档检查点检查动作
   - SummarizeDocument - 文档总结动作

3. **工具函数(Utils)**：
   - 文件读写函数
   - JSON处理函数
   - 检查点管理函数

4. **系统入口(document_checker.py)**：
   - 配置系统参数
   - 初始化和组装系统组件
   - 处理命令行参数
   - 执行文档检查流程

### 2.3 数据流

系统的数据流如下：

1. 用户提供需求文档和检查点定义文件
2. 系统读取并解析文档内容
3. 文档解析专家将文档转换为结构化数据
4. 文档检查专家根据检查点对文档进行评估
5. 文档总结专家汇总评估结果，生成最终报告
6. 系统输出详细的评估报告到指定文件

这一过程就像水流经过一系列过滤器和处理单元，最终形成纯净水一样，原始文档经过解析、检查、总结，最终形成高质量的评估报告。

## 3. 模块详细设计

### 3.1 角色模块 (Roles)

#### 3.1.1 DocumentParsingExpert（文档解析专家）

**职责**：解析原始文档，提取关键信息和结构化数据。

**主要属性**：
- `name`：角色名称，固定为 "DocumentParsingExpert"
- `profile`：角色描述，"文档解析专家，精通各类文档的解析和结构化处理"
- `goal`：角色目标，"精确解析文档内容，提取关键信息并转换为结构化数据"
- `constraints`：角色约束，"仅基于文档内容进行解析，不添加额外推测"
- `check_points_list`：检查点列表，用于指导解析关注点
- `parsed_document`：存储解析后的文档结构
- `original_document`：原始文档内容
- `current_check_point_index`：当前处理的检查点索引

**主要方法**：
- `__init__`：初始化解析专家，设置检查点列表
- `_act`：执行文档解析动作，处理用户输入或检查专家反馈

#### 3.1.2 DocumentCheckingExpert（文档检查专家）

**职责**：根据特定检查点评估文档内容，判断满足程度，提供改进建议。

**主要属性**：
- `name`：角色名称，固定为 "DocumentCheckingExpert"
- `profile`：角色描述，"文档检查专家"
- `check_results`：存储所有检查结果
- `output_file_path`：检查报告输出路径
- `check_points_definitions`：检查点详细定义
- `processed_check_points`：已处理的检查点集合
- `document_keywords_cache`：文档内容关键词缓存
- `disable_hallucination_detection`：是否禁用幻觉检测

**主要方法**：
- `__init__`：初始化检查专家，设置输出路径
- `_observe`：观察来自文档解析专家的消息
- `_get_check_point_definition`：获取检查点定义
- `_act`：执行检查动作，评估文档是否满足特定检查点
- `_format_check_point_definition`：格式化检查点定义
- `_validate_check_result`：验证检查结果中的引用是否存在于原文

#### 3.1.3 DocumentSummaryExpert（文档总结专家）

**职责**：汇总所有检查结果，生成最终综合评估报告。

**主要属性**：
- `name`：角色名称，固定为 "DocumentSummaryExpert"
- `profile`：角色描述，"文档总结专家"
- `output_file_path`：报告输出文件路径
- `check_results`：所有检查结果的集合

**主要方法**：
- `__init__`：初始化总结专家，设置输出路径
- `_observe`：观察来自文档检查专家的消息
- `_act`：执行总结动作，生成最终报告

### 3.2 动作模块 (Actions)

#### 3.2.1 ParseDocument（文档解析动作）

**职责**：解析文档内容，提取结构和关键信息。

**主要属性**：
- `name`：动作名称，"ParseDocument"
- `PROMPT_TEMPLATE`：提示模板，用于指导AI解析文档
- `CHECKPOINT_TO_FIELD_MAP`：检查点ID到解析字段的映射
- `check_points_list`：检查点列表

**主要方法**：
- `_build_dynamic_prompt`：根据检查点构建动态提示模板
- `run`：执行文档解析，返回结构化数据
- `_create_fallback_json`：创建备用JSON结果

#### 3.2.2 CheckDocumentPoint（检查点检查动作）

**职责**：根据特定检查点评估文档内容。

**主要属性**：
- `name`：动作名称，"CheckDocumentPoint"
- `PROMPT_TEMPLATE`：提示模板，用于指导AI评估文档

**主要方法**：
- `run`：执行检查点评估，返回评估结果

#### 3.2.3 SummarizeDocument（文档总结动作）

**职责**：汇总所有检查结果，生成综合报告。

**主要属性**：
- `name`：动作名称，"SummarizeDocument"
- `PROMPT_TEMPLATE`：提示模板，用于指导AI生成总结报告

**主要方法**：
- `run`：执行总结动作，生成最终报告

### 3.3 工具模块 (Utils)

#### 3.3.1 文件处理工具

**主要函数**：
- `read_document_file`：读取文档文件内容
- `read_json_file`：读取和解析JSON文件
- `fix_json_content`：修复损坏的JSON内容
- `prepare_output_file`：准备输出文件

#### 3.3.2 检查点管理工具

**主要函数**：
- `clean_check_points_list`：清理和去重检查点列表
- `get_default_check_points`：获取默认检查点列表
- `calculate_required_rounds`：计算完成所有检查点所需的轮数

### 3.4 系统入口 (document_checker.py)

**职责**：系统主入口，处理命令行参数，协调各组件运行。

**主要功能**：
- 解析命令行参数
- 读取文档和检查点定义
- 过滤不相关的检查点
- 创建和配置团队
- 执行文档检查流程
- 处理异常和错误

## 4. UML类图

### 4.1 角色类图

角色类图展示了系统中三个核心角色及其关系：

```
+-----------------------+        +-------------------------+        +-----------------------+
|DocumentParsingExpert  |        | DocumentCheckingExpert  |        | DocumentSummaryExpert |
+-----------------------+        +-------------------------+        +-----------------------+
|+name: str             |        |+name: str               |        |+name: str             |
|+profile: str          |        |+profile: str            |        |+profile: str          |
|+goal: str             |        |+check_results: List[str]|        |+output_file_path: str |
|+constraints: str      |------->|+output_file_path: str   |------->|+check_results: List   |
|+check_points_list: List|        |+check_points_definitions|        +-----------------------+
|+parsed_document: str   |        |+processed_check_points  |        |+__init__()            |
|+original_document: str |        |+disable_hallucination   |        |+_observe()            |
|+current_check_point_idx|        +-------------------------+        |+_act()                |
+-----------------------+        |+__init__()               |        +-----------------------+
|+__init__()             |        |+_observe()              |
|+_act()                 |        |+_get_check_point_def()  |
+-----------------------+        |+_act()                   |
                                  |+_format_check_point_def()|
                                  |+_validate_check_result() |
                                  +-------------------------+
```

### 4.2 动作类图

动作类图展示了系统中三个核心动作及其关系：

```
+----------------------------------+
|             Action               |
+----------------------------------+
|+name: str                        |
+----------------------------------+
              ^
              |
    +--------------------+--------------------+
    |                    |                    |
+---------------+  +-------------------+  +------------------+
| ParseDocument |  | CheckDocumentPoint|  | SummarizeDocument|
+---------------+  +-------------------+  +------------------+
|+name: str      |  |+name: str        |  |+name: str        |
|+PROMPT_TEMPLATE|  |+PROMPT_TEMPLATE  |  |+PROMPT_TEMPLATE  |
|+CHECKPOINT_MAP |  +-------------------+  +------------------+
|+check_points   |  |+run()             |  |+run()            |
+---------------+  +-------------------+  +------------------+
|+_build_prompt() |
|+run()           |
|+_create_fallback|
+---------------+
```

### 4.3 系统组件关系图

系统组件关系图展示了各个模块之间的关系：

```
+---------------------+     +------------------+     +----------------------+
| document_checker.py |---->| Team             |---->| DocumentParsingExpert|
+---------------------+     +------------------+     +----------------------+
        |                         |                          |
        |                         |                          |
        v                         v                          v
+---------------------+     +------------------+     +----------------------+
| check_points_def.json|     | DocumentChecking|     | ParseDocument       |
+---------------------+     | Expert           |     +----------------------+
                            +------------------+             |
                                    |                        |
                                    v                        v
                            +------------------+     +----------------------+
                            | DocumentSummary  |<----| CheckDocumentPoint   |
                            | Expert           |     +----------------------+
                            +------------------+
                                    |
                                    v
                            +------------------+
                            | SummarizeDocument|
                            +------------------+
                                    |
                                    v
                            +------------------+
                            | Output Report    |
                            +------------------+
```

## 5. 调用流程

### 5.1 系统整体调用流程

系统调用流程展示了从启动到生成报告的完整流程：

1. **初始化阶段**
   ```
   用户 → 执行document_checker.py脚本 → 读取文档和检查点定义 → 过滤检查点 → 配置系统参数
   ```

2. **团队构建阶段**
   ```
   创建文档解析专家 → 创建文档检查专家 → 创建文档总结专家 → 组建团队
   ```

3. **执行阶段**
   ```
   团队.run_project() → 将文档发送给解析专家 → 解析专家解析文档 → 开始执行团队协作流程
   ```

### 5.2 检查点处理流程

检查点处理流程展示了单个检查点的处理过程：

```
+-------------------+     +--------------------+     +--------------------+
| 文档解析专家      |---->| 文档检查专家       |---->| 文档总结专家       |
+-------------------+     +--------------------+     +--------------------+
| 1. 解析文档       |     | 3. 接收检查点      |     | 6. 接收所有检查结果|
| 2. 发送检查点     |<----| 4. A检查文档       |<----| 7. 生成综合报告    |
+-------------------+     | 5. 发送检查结果    |     +--------------------+
                           +--------------------+
```

检查点处理流程详细步骤：

1. 文档解析专家解析原始文档，提取结构化数据
2. 文档解析专家选择当前检查点，发送给文档检查专家
3. 文档检查专家接收检查点和解析后的文档
4. 文档检查专家评估文档是否满足该检查点要求
5. 文档检查专家发送检查结果
6. 如果还有未处理的检查点，回到步骤2继续处理下一个检查点
7. 当所有检查点处理完毕，文档解析专家发送完成消息给文档总结专家
8. 文档总结专家汇总所有检查结果，生成最终报告

### 5.3 幻觉检测流程

幻觉检测流程展示了系统如何验证AI生成内容的真实性：

```
+-----------------+     +-------------------+     +------------------+     +------------------+
| 检查专家收到结果 |---->| 提取引用内容      |---->| 在原文中搜索     |---->| 判断是否存在     |
+-----------------+     +-------------------+     +------------------+     +------------------+
                                                                                   |
                                                                                   v
                                 +------------------+     +------------------+
                                 | 添加警告标记     |<----| 如果不存在       |
                                 +------------------+     +------------------+
```

幻觉检测流程详细步骤：

1. 文档检查专家接收AI生成的检查结果
2. 提取检查结果中引用的文档原文内容（通常用引号标记）
3. 在原始文档中搜索这些引用内容
4. 判断引用内容是否确实存在于原始文档中
5. 如果引用内容不存在，添加警告标记，指出可能的幻觉
6. 将验证后的检查结果添加到结果集合中

## 6. 检查点定义结构

检查点定义使用JSON格式，结构如下：

```json
{
  "id": "检查点唯一标识符",
  "name": "检查点名称",
  "description": "检查点详细描述",
  "criteria": {
    "satisfied": "满足标准的描述",
    "partially_satisfied": "部分满足标准的描述",
    "not_satisfied": "不满足标准的描述"
  },
  "examples": {
    "satisfied": "满足标准的示例",
    "partially_satisfied": "部分满足标准的示例",
    "not_satisfied": "不满足标准的示例"
  }
}
```

每个检查点定义包含以下关键信息：
- **id**：检查点的唯一标识符
- **name**：检查点的名称，用于显示和引用
- **description**：检查点的详细描述
- **criteria**：评估标准，包括三个级别
  - **satisfied**：完全满足的标准
  - **partially_satisfied**：部分满足的标准
  - **not_satisfied**：不满足的标准
- **examples**：各级别的示例，帮助理解评估标准

## 7. 系统运行流程

### 7.1 命令行使用方式

系统通过命令行运行，支持多种参数配置。基本用法如下：

```bash
python document_checker.py --document_path="requirements.md" --output_path="check_report.md" --check_points_definition_path="check_points_definition.json" --investment=10.0 --n_round=15
```

参数说明：
- `--document_path`：待检查文档的路径
- `--output_path`：检查报告输出路径
- `--check_points_definition_path`：检查点定义文件路径
- `--investment`：团队投资金额，影响AI处理能力
- `--n_round`：最大运行轮数
- `--disable_filtering`：(可选)禁用检查点自动过滤
- `--disable_hallucination_detection`：(可选)禁用幻觉检测

### 7.2 运行时流程图

文档检查系统的完整运行时流程如下：

```
+------------------+     +------------------+     +------------------+
| 读取配置和文档   |---->| 构建检查点列表   |---->| 过滤不相关检查点 |
+------------------+     +------------------+     +------------------+
        |                                                 |
        v                                                 v
+------------------+     +------------------+     +------------------+
| 创建专家团队     |<----| 配置专家参数     |<----| 计算所需轮数     |
+------------------+     +------------------+     +------------------+
        |
        v
+------------------+     +------------------+     +------------------+
| 启动团队运行     |---->| 专家协作处理     |---->| 生成评估报告     |
+------------------+     +------------------+     +------------------+
```

### 7.3 典型运行场景

#### 7.3.1 基本检查场景

最简单的使用场景，只提供必要参数：

```bash
python document_checker.py --document_path="project_requirements.md"
```

系统将：
1. 读取文档内容
2. 使用默认检查点
3. 执行文档检查
4. 生成默认路径下的报告

就像简单地让医生做一次常规体检，只需要告诉医生"请给我做个体检"，医生会按照标准流程进行检查。

#### 7.3.2 自定义检查场景

提供完整的自定义参数配置：

```bash
python document_checker.py --document_path="detailed_specs.md" --output_path="comprehensive_report.md" --check_points_definition_path="extended_check_points.json" --investment=15.0 --n_round=20 --disable_filtering=True
```

系统将：
1. 读取文档内容
2. 使用自定义检查点定义
3. 保留所有检查点（禁用过滤）
4. 使用更高的投资和更多的轮数
5. 生成指定路径下的详细报告

这类似于要求医生做一次全面体检，并指定需要检查的各项指标，甚至包括一些可能与您情况不太相关的检查项。

## 8. 技术特点与优势

### 8.1 核心技术特点

1. **多智能体协作**：系统基于MetaGPT框架，采用多专家协作模式，各司其职，提高处理效率和准确性。

2. **动态提示生成**：系统根据检查点定义动态构建提示模板，确保AI解析和评估的准确性。

3. **幻觉检测**：具备验证AI引用内容的能力，防止生成不存在于原文的内容，提高评估可靠性。

4. **智能过滤**：能够智能分析文档类型和内容，过滤不相关的检查点，避免无效评估。

5. **结构化输出**：生成结构化的评估报告，便于阅读和理解，包含满足、部分满足和不满足的检查点分类。

### 8.2 系统优势

1. **适应性强**：能够处理各种类型的文档，包括需求文档、设计文档、API文档等。

2. **可扩展性好**：检查点定义采用JSON格式，易于扩展和定制，用户可根据需要添加新的检查点。

3. **高效协作**：借助MetaGPT的消息传递机制，各专家角色高效协作，形成完整工作流。

4. **防止幻觉**：内置幻觉检测机制，确保评估结果的真实性和可靠性。

5. **易于使用**：提供简洁的命令行接口，支持多种参数配置，适合各种使用场景。

## 9. 总结与展望

### 9.1 系统总结

文档分析系统是一个基于MetaGPT框架的智能文档检查工具，通过三个专家角色的协作完成文档的解析、检查和总结工作。系统能够评估文档的质量和完整性，生成详细的评估报告，并提供具体的改进建议。

系统的主要贡献包括：
1. 提供了一种自动化的文档质量评估方法
2. 实现了基于检查点的文档内容验证
3. 构建了防止AI幻觉的验证机制
4. 实现了多智能体协作的文档处理流程

### 9.2 未来展望

未来可考虑以下方向的扩展和改进：

1. **增强检查能力**：扩展检查点定义，覆盖更多文档质量维度，如一致性、可追溯性等。

2. **多文档对比**：支持多文档之间的关联分析和对比，检查不同文档之间的一致性和完整性。

3. **用户界面优化**：开发图形用户界面，使系统更易于使用，可视化展示检查结果。

4. **检查历史跟踪**：支持文档版本控制和检查历史跟踪，帮助用户了解文档质量的变化趋势。

5. **定制化评估标准**：支持用户定义和共享自己的评估标准和检查点，建立企业文档标准库。

6. **多语言支持**：扩展系统以支持多种语言的文档检查，增加国际化应用能力。

就像医疗技术在提供基础体检的同时不断发展出更精细、更全面的检查方法一样，文档分析系统也将不断进化，提供更智能、更全面的文档质量评估能力。 