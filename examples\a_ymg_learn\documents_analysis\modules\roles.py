"""
文件名: MetaGPT/examples/a_ymg_learn/documents_analysis/modules/roles.py
创建日期: 2024年
描述: 文档检查系统中的所有角色类实现
"""

import json
import os
from typing import Any, Dict, List, Set

from metagpt.actions import UserRequirement
from metagpt.logs import logger
from metagpt.roles import Role
from metagpt.schema import Message

from .actions import ParseDocument, CheckDocumentPoint, SummarizeDocument

class DocumentParsingExpert(Role):
    """文档解析专家，负责将文档解析为结构化内容"""

    name: str = "DocumentParsingExpert"
    profile: str = "文档解析专家，精通各类文档的解析和结构化处理"
    goal: str = "精确解析文档内容，提取关键信息并转换为结构化数据"
    constraints: str = "仅基于文档内容进行解析，不添加额外推测"
    next_role: str = "DocumentCheckingExpert"

    def __init__(self, check_points=None, **data: Any):
        """初始化文档解析专家

        Args:
            check_points: 检查点列表，将用于指导解析关注重点
            **data: 其他参数
        """
        super().__init__(**data)
        self.set_actions([ParseDocument])
        self._watch([UserRequirement])
        # 存储解析后的文档和检查点数据
        self.parsed_document = ""
        self.original_document = ""
        self.check_points = check_points or []
        self.check_points_list = check_points or []  # 为了兼容性添加
        self.current_check_point_index = 0
        logger.info(f"文档解析专家初始化完成，检查点数量: {len(self.check_points)}")

    async def _act(self) -> Message:
        """执行文档解析动作，并将结果传递给检查专家"""
        logger.info(f"{self._setting}: 执行 {self.rc.todo}({self.rc.todo.name})")
        todo = self.rc.todo  # ParseDocument 实例

        # 检查是否是来自检查专家的消息
        checking_msgs = [msg for msg in self.rc.news if msg.sent_from == "DocumentCheckingExpert"]
        if checking_msgs:
            # 已完成一个检查点，处理下一个
            last_msg = checking_msgs[-1]
            # 检查消息内容是否包含"跳过重复检查点"
            if "（跳过重复检查点" in last_msg.content:
                logger.info(f"收到跳过重复检查点的通知: {last_msg.content}")
                # 提取被跳过的检查点名称（如果有）
                import re
                match = re.search(r'（跳过重复检查点:\s*(.*?)）', last_msg.content)
                if match:
                    skipped_checkpoint = match.group(1)
                    logger.info(f"跳过重复检查点: {skipped_checkpoint}")
            
            logger.info(f"收到来自DocumentCheckingExpert的消息: {last_msg.content}")
            self.current_check_point_index += 1
            logger.info(f"处理下一个检查点，当前索引: {self.current_check_point_index}/{len(self.check_points)}")
        else:
            # 首次调用，解析文档和检查点
            memories = self.get_memories()
            logger.info(f"文档解析专家记忆: {memories}")
            
            # 从记忆中获取最新的消息内容
            user_requirement = memories[-1]
            document_content = user_requirement.content
            
            # 获取检查点列表（从传入的参数或使用默认值）
            if hasattr(self, 'check_points_list') and self.check_points_list:
                # 确保检查点列表中没有重复项
                unique_check_points = []
                seen_check_points = set()
                
                for cp in self.check_points_list:
                    if cp not in seen_check_points:
                        unique_check_points.append(cp)
                        seen_check_points.add(cp)
                    else:
                        logger.warning(f"检查点列表中存在重复项: {cp}")
                
                self.check_points = unique_check_points
                logger.info(f"使用自定义检查点列表，去重后共有 {len(self.check_points)} 个检查点")
            else:
                # 使用默认检查点列表
                self.check_points = [
                    "文档是否包含明确的功能需求",
                    "文档是否包含系统接口定义",
                    "文档是否包含明确的截止时间",
                    "文档是否包含开发人员信息"
                ]
                logger.info(f"使用默认检查点列表，共有 {len(self.check_points)} 个检查点")
            
            # 重置当前检查点索引
            self.current_check_point_index = 0
            
            # 执行文档解析
            logger.info(f"将执行文档解析，共 {len(self.check_points)} 个检查点")
            
            # 创建一个修改后的ParseDocument副本并运行
            # 避免直接修改类变量或实例变量
            parse_action_instance = ParseDocument()
            # 在运行时将检查点列表作为参数传递
            self.parsed_document = await todo.run(
                document_content=document_content, 
                check_points=self.check_points
            )
            
            self.original_document = document_content
            logger.info(f"文档已解析，共有 {len(self.check_points)} 个检查点需要检查")
        
        # 如果所有检查点都已处理完毕，则返回完成消息
        if self.current_check_point_index >= len(self.check_points):
            logger.info("所有检查点已处理完毕")
            return Message(
                content="所有检查点已处理完毕",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={self.next_role},
            )
        
        # 获取当前需要处理的检查点
        current_check_point = self.check_points[self.current_check_point_index]
        
        # 创建传递给检查专家的消息，包含当前检查点和解析后的文档
        msg_content = {
            "check_point": current_check_point,
            "check_point_index": self.current_check_point_index,
            "total_check_points": len(self.check_points),
            "parsed_document": self.parsed_document,
            "original_document": self.original_document
        }
        
        msg = Message(
            content=json.dumps(msg_content),
            role=self.profile,
            cause_by=type(todo),
            sent_from=self.name,
            send_to={self.next_role},
        )
        self.rc.memory.add(msg)

        return msg


class DocumentCheckingExpert(Role):
    """文档检查专家角色"""
    name: str = "DocumentCheckingExpert"
    profile: str = "文档检查专家"
    next_role: str = "DocumentSummaryExpert"

    def __init__(self, **data: Any):
        super().__init__(**data)
        self.set_actions([CheckDocumentPoint])
        self._watch([ParseDocument])
        # 存储检查结果
        self.check_results = []
        # 存储输出文件路径
        self.output_file_path = data.get("output_file_path", "document_check_report.md")
        # 存储检查点定义
        self.check_points_definitions = []
        # 存储已处理过的检查点，避免重复处理
        self.processed_check_points = set()
        # 存储文档内容的关键词缓存，用于快速判断相关性
        self.document_keywords_cache = {}
        # 设置文档类型
        self.document_type = "unknown"
        # 是否禁用幻觉检测
        self.disable_hallucination_detection = data.get("disable_hallucination_detection", False)

    async def _observe(self) -> int:
        """观察来自文档解析专家的消息"""
        await super()._observe()
        # 只接收发送给自己的消息
        self.rc.news = [msg for msg in self.rc.news if self.name in msg.send_to]
        return len(self.rc.news)
    
    def _get_check_point_definition(self, check_point: str) -> Dict:
        """根据检查点名称获取其定义

        Args:
            check_point: 检查点名称

        Returns:
            Dict: 检查点定义，如果没有找到则返回空字典
        """
        if not self.check_points_definitions:
            logger.warning(f"检查点定义列表为空，无法为检查点 '{check_point}' 提供标准定义")
            return {}
            
        # 尝试通过名称精确匹配
        for definition in self.check_points_definitions:
            if definition.get("name") == check_point:
                logger.info(f"找到检查点 '{check_point}' 的精确匹配定义")
                return definition
                
        # 尝试通过描述匹配（作为备选方案）
        for definition in self.check_points_definitions:
            if definition.get("description") == check_point:
                logger.info(f"通过描述找到检查点 '{check_point}' 的匹配定义")
                return definition
                
        # 尝试通过ID匹配
        for definition in self.check_points_definitions:
            # 检查点可能使用ID作为标识
            point_id = self._extract_check_point_id(check_point)
            if point_id and (definition.get("id") == point_id or point_id in str(definition.get("id", ""))):
                logger.info(f"通过ID '{point_id}' 找到检查点 '{check_point}' 的匹配定义")
                return definition
        
        # 未找到匹配的定义
        logger.warning(f"未找到检查点 '{check_point}' 的匹配定义")
        return {}
    
    def _extract_check_point_id(self, check_point: str) -> str:
        """从检查点文本中提取可能的ID
        
        Args:
            check_point: 检查点文本
            
        Returns:
            str: 可能的ID，如果没有找到则返回空字符串
        """
        # 常见的ID模式
        import re
        
        # 尝试匹配明确的ID格式，如"CP-001"或"checkpoint_1"
        id_patterns = [
            r'([A-Z]+-\d+)',          # 如 "CP-001"
            r'(\d+)',                  # 纯数字如 "1"
            r'([a-z]+_[a-z_]+)',      # snake_case 如 "functionality_requirements"
            r'([a-z]+\.[a-z_]+)',     # dot notation 如 "req.functionality"
            r'\b([A-Z]{2,})\b'        # 缩写如 "FR" (Functional Requirements)
        ]
        
        for pattern in id_patterns:
            match = re.search(pattern, check_point)
            if match:
                return match.group(1).lower()
                
        return ""

    def _build_document_keyword_cache(self, document_content: str):
        """构建文档关键词缓存，用于快速判断内容相关性
        
        Args:
            document_content: 文档内容
        """
        if self.document_keywords_cache:
            return  # 已经构建过缓存
            
        # 文档长度
        self.document_keywords_cache["length"] = len(document_content)
        
        # 关键内容类别词汇
        content_categories = {
            "开发人员": ["团队", "人员", "开发者", "工程师", "联系方式", "姓名", "职责", "开发团队"],
            "预算": ["成本", "费用", "金额", "开销", "资金", "预算", "投资", "百万", "万元"],
            "接口": ["API", "接口", "调用", "服务", "端口", "协议", "RESTful"],
            "截止日期": ["截止", "日期", "期限", "完成时间", "交付时间", "完成于", "年", "月", "日", "季度"],
            "UI": ["界面", "UI", "用户体验", "交互", "前端", "设计", "样式", "布局"]
        }
        
        # 检测各类别关键词在文档中的存在情况
        category_presence = {}
        document_lower = document_content.lower()
        
        for category, keywords in content_categories.items():
            keywords_found = [kw for kw in keywords if kw in document_lower]
            category_presence[category] = {
                "present": len(keywords_found) > 0,
                "keywords_found": keywords_found
            }
            
        self.document_keywords_cache["categories"] = category_presence
        
        # 检测文档类型
        doc_type_keywords = {
            "requirements": ["需求", "要求", "功能", "特性", "实现", "系统应", "应包含", "应支持"],
            "api": ["接口", "API", "调用", "请求", "响应", "服务", "端点", "参数", "返回值"],
            "design": ["设计", "架构", "结构", "模型", "流程", "类图", "组件", "模块"],
            "plan": ["计划", "进度", "时间表", "里程碑", "排期", "阶段", "交付", "期限"]
        }
        
        type_scores = {}
        for t_name, keywords in doc_type_keywords.items():
            keywords_found = [kw for kw in keywords if kw in document_lower]
            type_scores[t_name] = len(keywords_found)
        
        if not type_scores or max(type_scores.values()) == 0:
            self.document_type = "requirements"  # 默认类型
        else:
            self.document_type = max(type_scores, key=type_scores.get)
            
        self.document_keywords_cache["document_type"] = self.document_type
        logger.info(f"文档类型判定为: {self.document_type}")

    def _is_check_point_relevant(self, check_point: str, check_point_definition: Dict, document_content: str) -> tuple:
        """判断检查点是否适用于当前文档类型
        
        Args:
            check_point: 检查点名称
            check_point_definition: 检查点定义
            document_content: 文档内容
            
        Returns:
            tuple: (是否适用, 不适用原因)
        """
        if not self.document_keywords_cache:
            self._build_document_keyword_cache(document_content)
        
        # 检查点文本转小写
        check_point_lower = check_point.lower()
        
        # 基于缓存的类别信息快速判断
        category_presence = self.document_keywords_cache.get("categories", {})
        is_short_doc = self.document_keywords_cache.get("length", 1000) < 800
        
        # 检查不同类别的相关性
        irrelevant_categories = {
            "开发人员": ["人员", "团队", "开发者", "联系人", "工程师", "developer", "team"],
            "预算": ["预算", "成本", "费用", "资金", "投入", "budget", "cost"],
            "接口": ["接口", "api", "服务", "调用"],
            "ui": ["界面", "ui", "用户界面", "交互"]
        }
        
        # 检查是否是特定类别的检查点
        for category, keywords in irrelevant_categories.items():
            if any(kw in check_point_lower for kw in keywords):
                # 检查文档中是否有这类内容
                category_info = category_presence.get(category, {"present": False})
                if not category_info.get("present", False):
                    # 如果文档中没有任何相关内容，且是短文档或API/设计文档，则判定不相关
                    if is_short_doc or self.document_type in ["api", "design"] and category in ["开发人员", "预算"]:
                        return False, f"文档中不包含任何{category}相关内容"
        
        # 特定文档类型的过滤条件
        doc_type_filters = {
            "api": {
                "不相关": ["ui", "用户界面", "ui_requirements", "开发人员", "团队", "预算"],
                "原因": "API文档通常不包含UI或团队/预算信息"
            },
            "design": {
                "不相关": ["开发人员", "团队", "预算"],
                "原因": "设计文档通常不包含团队/预算信息"
            }
        }
        
        # 根据文档类型过滤检查点
        doc_type = self.document_keywords_cache.get("document_type", "requirements")
        if doc_type in doc_type_filters:
            irrelevant_terms = doc_type_filters[doc_type]["不相关"]
            if any(term in check_point_lower for term in irrelevant_terms):
                return False, f"{doc_type_filters[doc_type]['原因']}"
        
        # 检查点定义中的额外信息
        if check_point_definition:
            applicable_doc_types = check_point_definition.get("applicable_doc_types", [])
            if applicable_doc_types and isinstance(applicable_doc_types, list) and doc_type not in applicable_doc_types:
                return False, f"检查点仅适用于{','.join(applicable_doc_types)}类型文档"
        
        return True, ""

    async def _act(self) -> Message:
        """执行文档检查动作，并将结果传递给总结专家"""
        logger.info(f"{self._setting}: 执行 {self.rc.todo}({self.rc.todo.name})")
        todo = self.rc.todo  # CheckDocumentPoint 实例

        # 获取来自文档解析专家的消息
        doc_parsing_msgs = [msg for msg in self.rc.news if msg.sent_from == "DocumentParsingExpert"]
        
        # 获取来自幻觉检查者的消息
        hallucination_msgs = [msg for msg in self.rc.news if msg.sent_from == "HallucinationDetector"]
        
        # 优先处理来自幻觉检查者的消息（重新评估）
        if hallucination_msgs:
            latest_hall_msg = hallucination_msgs[-1]
            try:
                # 解析幻觉检查者的反馈
                hall_data = json.loads(latest_hall_msg.content)
                verification_result = hall_data.get("verification_result", "")
                
                if verification_result == "failed":
                    # 需要重新评估
                    check_point = hall_data.get("check_point", "")
                    feedback = hall_data.get("feedback", "")
                    original_document = hall_data.get("original_document", "")
                    
                    logger.warning(f"收到幻觉检查者反馈，需要重新评估检查点: {check_point}")
                    logger.info(f"幻觉检查者反馈: {feedback}")
                    
                    # 从记忆中找出该检查点的解析文档
                    parsed_document = "{}"
                    doc_msgs = self.rc.memory.get_by_role("DocumentParsingExpert")
                    for msg in reversed(doc_msgs):
                        try:
                            msg_data = json.loads(msg.content)
                            if msg_data.get("check_point") == check_point:
                                parsed_document = msg_data.get("parsed_document", "{}")
                                break
                        except:
                            continue
                    
                    # 重新执行文档检查，加入幻觉检查者的反馈
                    enhanced_prompt = f"""
                    ## 重要反馈
                    你之前的评估结果存在逻辑不一致问题。请仔细阅读以下反馈，并在重新评估时避免这些问题：
                    
                    {feedback}
                    
                    请确保你的评估完全基于文档中实际存在的内容，不要引用不存在的内容或做出不合理的推断。
                    """
                    
                    # 创建CheckDocumentPoint的增强版本并运行
                    logger.info(f"重新评估检查点: {check_point}")
                    check_result = await self.rc.todo.run(
                        parsed_document=parsed_document,
                        original_document=original_document,
                        check_point=check_point,
                        enhanced_prompt=enhanced_prompt  # 将反馈传递给Action
                    )
                    
                    # 后处理：检查评估结果中是否出现幻觉
                    check_result = self._validate_check_result(check_result, original_document)
                    
                    # 更新检查结果列表中对应的结果
                    for i, result in enumerate(self.check_results):
                        if check_point in result:
                            self.check_results[i] = check_result
                            logger.info(f"已更新检查点 '{check_point}' 的评估结果")
                            break
                    
                    # 将更新后的结果保存到报告文件
                    try:
                        # 先找出检查点的索引
                        check_point_index = -1
                        for i, cp in enumerate(self.check_points_definitions):
                            if cp.get("name") == check_point:
                                check_point_index = i
                                break
                        
                        with open(self.output_file_path, 'r', encoding='utf-8') as f:
                            report_lines = f.readlines()
                        
                        # 查找检查点在报告中的位置
                        start_line = -1
                        end_line = -1
                        for i, line in enumerate(report_lines):
                            if f"## 检查点 {check_point_index+1}: {check_point}" in line:
                                start_line = i
                            elif start_line != -1 and line.startswith("## 检查点"):
                                end_line = i
                                break
                        
                        if start_line != -1:
                            # 如果找到检查点的起始位置
                            if end_line == -1:
                                end_line = len(report_lines)  # 如果是最后一个检查点
                            
                            # 替换检查点结果
                            new_content = f"## 检查点 {check_point_index+1}: {check_point} (重新评估)\n\n"
                            # 逐行处理并去除每行的空白
                            for line in check_result.strip().split('\n'):
                                new_content += f"{line.strip()}\n"
                            new_content += "\n"
                            report_lines[start_line:end_line] = [new_content]
                            
                            with open(self.output_file_path, 'w', encoding='utf-8') as f:
                                f.writelines(report_lines)
                            
                            logger.info(f"已更新报告文件中检查点 {check_point_index+1} 的结果")
                        else:
                            # 如果找不到，则追加到文件末尾
                            with open(self.output_file_path, 'a', encoding='utf-8') as f:
                                f.write(f"## 检查点 {check_point_index+1}: {check_point} (重新评估)\n\n")
                                # 逐行处理并去除每行的空白
                                for line in check_result.strip().split('\n'):
                                    f.write(f"{line.strip()}\n")
                                f.write("\n")
                    except Exception as e:
                        logger.error(f"更新检查点结果到文件时出错: {e}")
                    
                    # 再次将结果发送给幻觉检查者进行验证
                    return Message(
                        content=json.dumps({
                            "check_point": check_point,
                            "check_result": check_result,
                            "original_document": original_document
                        }),
                        role=self.profile,
                        cause_by=type(todo),
                        sent_from=self.name,
                        send_to={"HallucinationDetector"},
                    )
                elif verification_result == "passed":
                    # 验证通过，继续处理下一个检查点
                    check_point = hall_data.get("check_point", "")
                    logger.info(f"检查点 '{check_point}' 的评估通过幻觉检查")
                    
                    # 返回处理下一个检查点的消息
                    return Message(
                        content=f"已完成检查点评估并通过幻觉检查: {check_point}",
                        role=self.profile,
                        cause_by=type(todo),
                        sent_from=self.name,
                        send_to={"DocumentParsingExpert"},
                    )
            except Exception as e:
                logger.error(f"处理幻觉检查者反馈时出错: {e}")
                import traceback
                logger.error(f"异常详情: {traceback.format_exc()}")
        
        # 如果没有幻觉检查者的消息或者处理完成，继续处理来自解析专家的消息
        if not doc_parsing_msgs:
            logger.warning("未收到文档解析专家的消息")
            return Message(
                content="未收到文档解析专家的消息",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={self.next_role},
            )
        
        # 获取最新消息
        latest_msg = doc_parsing_msgs[-1]
        
        # 如果收到的是完成消息，则将结果传递给总结专家
        if latest_msg.content == "所有检查点已处理完毕":
            # 创建传递给总结专家的消息，包含所有检查结果
            msg = Message(
                content=json.dumps({"check_results": self.check_results}),
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={self.next_role},
            )
            self.rc.memory.add(msg)
            return msg
        
        try:
            # 解析消息内容（JSON格式）
            message_content = json.loads(latest_msg.content)
            
            # 获取当前检查点和文档内容
            check_point = message_content.get("check_point", "")
            check_point_index = message_content.get("check_point_index", 0)
            total_check_points = message_content.get("total_check_points", 0)
            parsed_document = message_content.get("parsed_document", "{}")
            original_document = message_content.get("original_document", "")
            
            if not check_point:
                logger.warning("消息中未包含检查点信息")
                return Message(
                    content="（跳过空检查点）",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"DocumentParsingExpert"},
                )
            
            # 如果检查点已经处理过，跳过
            if check_point in self.processed_check_points:
                logger.warning(f"检查点 '{check_point}' 已经被处理过，跳过")
                return Message(
                    content=f"（跳过重复检查点: {check_point}）",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"DocumentParsingExpert"},
                )
            
            # 初始化文档关键词缓存（如果还没有）
            if not self.document_keywords_cache:
                self._build_document_keyword_cache(original_document)
            
            # 获取检查点定义（如果有）
            check_point_definition = self._get_check_point_definition(check_point)
            
            # 预检查: 评估检查点是否适用于当前文档类型
            is_relevant, irrelevant_reason = self._is_check_point_relevant(check_point, check_point_definition, original_document)
            
            if not is_relevant:
                logger.warning(f"检查点 '{check_point}' 与当前文档类型不相关: {irrelevant_reason}")
                
                # 创建不相关检查点的评估结果
                irrelevant_result = f"""检查点：{check_point}
评估结果：不满足
详细说明：该检查点与当前文档不相关。{irrelevant_reason}。文档类型为 {self.document_type}，而该检查点要求的内容在这类文档中通常不存在或不适用。
改进建议：对于此类型的文档，通常不需要添加此检查点所要求的内容。如果确实需要，请考虑在单独的文档中添加相关信息。"""
                
                # 添加检查结果到列表
                self.check_results.append(irrelevant_result.strip())
                
                # 标记该检查点已处理
                self.processed_check_points.add(check_point)
                
                # 将检查结果保存到报告文件
                try:
                    with open(self.output_file_path, 'a', encoding='utf-8') as f:
                        f.write(f"## 检查点 {check_point_index+1}: {check_point} (不相关)\n\n")
                        # 逐行处理并去除每行的空白
                        for line in irrelevant_result.strip().split('\n'):
                            f.write(f"{line.strip()}\n")
                        f.write("\n")
                    logger.info(f"已将不相关检查点 {check_point_index+1} 的结果保存到 {self.output_file_path}")
                except Exception as e:
                    logger.error(f"保存检查点结果到文件时出错: {e}")
                
                # 将结果发送给幻觉检查者进行验证
                if self.disable_hallucination_detection:
                    # 如果禁用了幻觉检测，直接将结果发送给解析专家处理下一个检查点
                    logger.info("幻觉检测已禁用，直接将结果发送给解析专家")
                    return Message(
                        content=f"检查点 '{check_point}' 评估完成（不相关）",
                        role=self.profile,
                        cause_by=type(todo),
                        sent_from=self.name,
                        send_to={"DocumentParsingExpert"},
                    )
                else:
                    # 启用了幻觉检测，发送结果给幻觉检查者验证
                    return Message(
                        content=json.dumps({
                            "check_point": check_point,
                            "check_result": irrelevant_result.strip(),
                            "original_document": original_document
                        }),
                        role=self.profile,
                        cause_by=type(todo),
                        sent_from=self.name,
                        send_to={"HallucinationDetector"},
                    )
            
            # 对检查点定义进行增强和格式化，使其更易于AI理解
            if check_point_definition:
                enhanced_definition = self._format_check_point_definition(check_point_definition)
                # 将格式化后的定义包含在传递给AI的消息中
                try:
                    parsed_json = json.loads(parsed_document) if parsed_document else {}
                    parsed_json["check_point_definition"] = enhanced_definition
                    parsed_document = json.dumps(parsed_json, ensure_ascii=False)
                    logger.info(f"为检查点 {check_point} 添加了格式化的标准定义")
                except Exception as e:
                    logger.error(f"添加检查点定义到parsed_document时出错: {e}")
            else:
                logger.warning(f"没有找到检查点 '{check_point}' 的定义，将使用默认评估标准")
            
            # 执行文档检查
            logger.info(f"执行检查点 {check_point_index+1}/{total_check_points}: {check_point}")
            check_result = await todo.run(
                parsed_document=parsed_document,
                original_document=original_document,
                check_point=check_point
            )
            
            # 后处理：检查评估结果中是否出现幻觉
            check_result = self._validate_check_result(check_result, original_document)
            
            # 添加检查结果到列表
            self.check_results.append(check_result)
            
            # 标记该检查点已处理
            self.processed_check_points.add(check_point)
            
            # 将检查结果保存到报告文件
            try:
                with open(self.output_file_path, 'a', encoding='utf-8') as f:
                    f.write(f"## 检查点 {check_point_index+1}: {check_point}\n\n")
                    # 逐行处理并去除每行的空白
                    for line in check_result.strip().split('\n'):
                        f.write(f"{line.strip()}\n")
                    f.write("\n")
                logger.info(f"已将检查点 {check_point_index+1} 的结果保存到 {self.output_file_path}")
            except Exception as e:
                logger.error(f"保存检查点结果到文件时出错: {e}")
            
            # 将结果发送给幻觉检查者进行验证
            if self.disable_hallucination_detection:
                # 如果禁用了幻觉检测，直接将结果发送给解析专家处理下一个检查点
                logger.info("幻觉检测已禁用，直接将结果发送给解析专家")
                return Message(
                    content=f"检查点 '{check_point}' 评估完成（不相关）",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"DocumentParsingExpert"},
                )
            else:
                # 启用了幻觉检测，发送结果给幻觉检查者验证
                return Message(
                    content=json.dumps({
                        "check_point": check_point,
                        "check_result": check_result,
                        "original_document": original_document
                    }),
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"HallucinationDetector"},
                )
            
        except Exception as e:
            logger.error(f"处理检查点时出错: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            # 创建错误消息，跳过当前检查点
            return Message(
                content=f"处理检查点时出错: {e}",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={"DocumentParsingExpert"},
            )
            
        # 检查是否已经处理了所有检查点
        if len(self.check_results) >= total_check_points:
            logger.info(f"所有检查点({total_check_points}个)已全部评估完成")
            
            # 所有检查点都已评估完成，将结果发送给总结专家
            return Message(
                content=json.dumps({
                    "check_results": self.check_results,
                    "total_check_points": total_check_points
                }),
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={self.next_role},
            )

    def _format_check_point_definition(self, definition: Dict) -> Dict:
        """格式化检查点定义，使其更易于AI理解
        
        Args:
            definition: 原始检查点定义
            
        Returns:
            Dict: 格式化后的检查点定义
        """
        formatted = {}
        
        # 基本信息
        formatted["name"] = definition.get("name", "")
        formatted["description"] = definition.get("description", "")
        
        # 检查点定义格式处理
        # 获取标准和示例，处理不同的结构格式
        criteria = definition.get("criteria", {})
        examples = definition.get("examples", {})
        
        # 格式化示例和标准 - 处理嵌套结构
        formatted["satisfied"] = {
            "标准": criteria.get("satisfied") if criteria else definition.get("satisfied_criteria", "要求完全满足"),
            "示例": examples.get("satisfied") if examples else definition.get("satisfied_examples", [])
        }
        
        formatted["partially_satisfied"] = {
            "标准": criteria.get("partially_satisfied") if criteria else definition.get("partially_satisfied_criteria", "部分满足要求"),
            "示例": examples.get("partially_satisfied") if examples else definition.get("partially_satisfied_examples", [])
        }
        
        formatted["not_satisfied"] = {
            "标准": criteria.get("not_satisfied") if criteria else definition.get("not_satisfied_criteria", "完全不满足要求"),
            "示例": examples.get("not_satisfied") if examples else definition.get("not_satisfied_examples", [])
        }
        
        # 记录字段提取情况，便于调试
        logger.info(f"检查点定义格式化: ID={definition.get('id', 'unknown')}, 名称={formatted['name']}")
        logger.info(f"  提取到标准: satisfied={formatted['satisfied']['标准'] != '要求完全满足'}, partially={formatted['partially_satisfied']['标准'] != '部分满足要求'}, not={formatted['not_satisfied']['标准'] != '完全不满足要求'}")
        
        # 添加适用的文档类型
        formatted["applicable_doc_types"] = definition.get("applicable_doc_types", [])
        
        return formatted
    
    def _validate_check_result(self, check_result: str, original_document: str) -> str:
        """验证评估结果中是否包含幻觉，如发现则自动修正
        
        Args:
            check_result: 评估结果
            original_document: 原始文档内容
            
        Returns:
            str: 修正后的评估结果
        """
        # 如果禁用了幻觉检测，直接返回原始结果
        if self.disable_hallucination_detection:
            logger.info("幻觉检测已禁用，直接使用原始评估结果")
            return check_result
            
        import re
        
        # 提取评估结果中引用的"文档内容"
        # 查找引号内的内容，可能是引用的文档文本
        quoted_texts = re.findall(r'"([^"]*)"', check_result)
        
        # 检查每个引用是否真实存在于原始文档中
        hallucinated_quotes = []
        for quote in quoted_texts:
            if len(quote) > 5 and quote not in original_document:  # 忽略过短的引用
                hallucinated_quotes.append(quote)
        
        # 如果发现幻觉内容
        if hallucinated_quotes:
            logger.warning(f"在评估结果中发现 {len(hallucinated_quotes)} 处疑似幻觉引用")
            
            # 记录发现的幻觉内容
            for i, quote in enumerate(hallucinated_quotes):
                logger.warning(f"  幻觉内容 {i+1}: '{quote}'")
            
            # 提取评估结果的各个部分
            result_parts = re.split(r'\n(?=检查点：|评估结果：|详细说明：|改进建议：)', check_result)
            
            # 分析评估结果
            result_type = "不满足"  # 默认设为"不满足"
            for part in result_parts:
                if part.startswith("评估结果："):
                    current_result = part.replace("评估结果：", "").strip()
                    if current_result in ["满足", "部分满足", "不满足"]:
                        result_type = current_result
            
            # 如果评估结果是"满足"或"部分满足"，但引用内容不存在，则修改为"不满足"
            if result_type in ["满足", "部分满足"] and hallucinated_quotes:
                logger.warning(f"由于发现幻觉引用，将评估结果从 '{result_type}' 修改为 '不满足'")
                
                # 替换评估结果
                for i, part in enumerate(result_parts):
                    if part.startswith("评估结果："):
                        result_parts[i] = "评估结果：不满足"
                    elif part.startswith("详细说明："):
                        explanation = part.replace("详细说明：", "").strip()
                        # 先构建引用文本列表
                        quoted_texts = [f'"{q}"' for q in hallucinated_quotes[:2]]
                        result_parts[i] = f"详细说明：文档中没有包含此检查点所要求的内容。评估系统可能错误地引用了不存在的内容: {', '.join(quoted_texts)}。经过验证，这些内容在原始文档中并不存在。"
                    elif part.startswith("改进建议："):
                        result_parts[i] = "改进建议：建议在文档中添加此检查点所要求的内容，确保信息完整且符合检查标准。"
                
                # 重新组合评估结果
                check_result = "\n".join(result_parts)
        
        return check_result


class DocumentSummaryExpert(Role):
    """文档总结专家角色"""
    name: str = "DocumentSummaryExpert"
    profile: str = "文档总结专家"

    def __init__(self, **data: Any):
        super().__init__(**data)
        self.set_actions([SummarizeDocument])
        self._watch([CheckDocumentPoint])
        # 存储输出文件路径
        self.output_file_path = data.get("output_file_path", "document_check_report.md")

    async def _observe(self) -> int:
        """观察来自检查专家的消息"""
        await super()._observe()
        # 只接收发送给自己的消息
        self.rc.news = [msg for msg in self.rc.news if self.name in msg.send_to]
        return len(self.rc.news)

    async def _act(self) -> Message:
        """执行文档总结动作，生成最终报告"""
        logger.info(f"{self._setting}: 执行 {self.rc.todo}({self.rc.todo.name})")
        todo = self.rc.todo  # SummarizeDocument 实例

        # 获取来自检查专家的消息
        check_msgs = [msg for msg in self.rc.news if msg.sent_from == "DocumentCheckingExpert"]
        if not check_msgs:
            logger.warning("未收到文档检查专家的消息")
            return Message(
                content="未收到文档检查专家的消息",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to=set(),  # 最终结果不需要发送给其他角色
            )
        
        # 从最新消息中获取检查结果
        latest_msg = check_msgs[-1]
        
        try:
            # 解析消息内容（JSON格式）
            message_content = json.loads(latest_msg.content)
            check_results = message_content.get("check_results", [])
            
            if not check_results:
                logger.warning("没有检查结果可供总结")
                return Message(
                    content="没有检查结果可供总结",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to=set(),  # 最终结果不需要发送给其他角色
                )
            
            # 执行文档总结
            summary = await todo.run(check_results=check_results)
            
            # 将总结添加到报告文件末尾
            try:
                with open(self.output_file_path, 'a', encoding='utf-8') as f:
                    f.write(f"# 总结报告\n\n")
                    f.write(f"{summary}\n")
                logger.info(f"已将总结报告保存到 {self.output_file_path}")
            except Exception as e:
                logger.error(f"保存总结报告到文件时出错: {e}")
            
            # 创建最终报告消息
            msg = Message(
                content=summary,
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to=set(),  # 最终结果不需要发送给其他角色
            )
            self.rc.memory.add(msg)

            return msg
            
        except Exception as e:
            logger.error(f"处理总结时出错: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            # 创建错误消息
            return Message(
                content=f"生成总结报告时出错: {e}",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to=set(),  # 最终结果不需要发送给其他角色
            )

class HallucinationDetector(Role):
    """幻觉检查者角色，负责验证文档检查结果的逻辑一致性"""
    
    name: str = "HallucinationDetector"
    profile: str = "幻觉检查专家"
    goal: str = "验证检查结果与原始文档的逻辑一致性，发现并纠正可能的幻觉和逻辑错误"
    constraints: str = "仅基于事实和逻辑进行分析，不添加个人判断"
    
    def __init__(self, **data: Any):
        super().__init__(**data)
        self.set_actions([VerifyLogicalConsistency])
        self._watch([CheckDocumentPoint])
        # 存储验证历史
        self.verification_history = []
        # 定义阈值，用于判断验证结果
        self.consistency_threshold = 0.8  # 80%的一致性被认为是可接受的
        # 是否禁用幻觉检测
        self.disable_hallucination_detection = data.get("disable_hallucination_detection", False)
        if self.disable_hallucination_detection:
            logger.info("幻觉检查者被创建，但幻觉检测功能已禁用，将只记录日志不执行实际验证")
    
    async def _observe(self) -> int:
        """观察来自检查专家的消息"""
        await super()._observe()
        # 只接收发送给自己的消息
        self.rc.news = [msg for msg in self.rc.news if self.name in msg.send_to]
        return len(self.rc.news)
    
    async def _act(self) -> Message:
        """执行逻辑一致性验证动作"""
        logger.info(f"{self._setting}: 执行 {self.rc.todo}({self.rc.todo.name})")
        todo = self.rc.todo  # VerifyLogicalConsistency 实例

        # 如果幻觉检测被禁用，则只记录日志但不执行实际验证
        if self.disable_hallucination_detection:
            logger.info("幻觉检测功能已禁用，HallucinationDetector仅记录日志不执行验证")
            
            # 获取来自检查专家的消息
            check_msgs = [msg for msg in self.rc.news if msg.sent_from == "DocumentCheckingExpert"]
            if not check_msgs:
                logger.warning("未收到文档检查专家的消息")
                return Message(
                    content="未收到文档检查专家的消息，且幻觉检测已禁用",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"DocumentCheckingExpert"},
                )
            
            # 获取最新消息
            latest_msg = check_msgs[-1]
            
            try:
                # 从消息中提取检查点信息
                message_content = latest_msg.content
                logger.info(f"幻觉检查者收到消息（但不执行验证）：{message_content[:100]}...")
                
                # 尝试解析JSON
                try:
                    data = json.loads(message_content)
                    check_point = data.get("check_point", "")
                    logger.info(f"检查点 '{check_point}' 的评估结果不进行验证（幻觉检测已禁用），直接返回通过结果")
                except:
                    check_point = "未知检查点"
                    logger.warning("无法解析消息内容，使用默认检查点名")
                
                # 直接返回验证通过的消息
                return Message(
                    content=json.dumps({
                        "verification_result": "passed",
                        "check_point": check_point,
                        "assessment": "幻觉检测已禁用，不执行实际验证，直接返回通过结果"
                    }),
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"DocumentCheckingExpert"},
                )
            except Exception as e:
                logger.error(f"处理消息时出错: {e}")
                return Message(
                    content="处理消息出错，且幻觉检测已禁用",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"DocumentCheckingExpert"},
                )
        
        # 以下是正常的验证逻辑（幻觉检测未禁用）
        # 获取来自检查专家的消息
        check_msgs = [msg for msg in self.rc.news if msg.sent_from == "DocumentCheckingExpert"]
        if not check_msgs:
            logger.warning("未收到文档检查专家的消息")
            return Message(
                content="未收到文档检查专家的消息",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={"DocumentCheckingExpert"},
            )
        
        # 获取最新消息
        latest_msg = check_msgs[-1]
        
        try:
            # 解析消息内容
            message_content = latest_msg.content
            logger.info(f"幻觉检查者收到消息：{message_content[:100]}...")
            
            # 从消息中提取必要信息
            # 假设检查专家的消息格式为：
            # {
            #   "check_point": "检查点内容",
            #   "check_result": "评估结果内容",
            #   "original_document": "原始文档内容"
            # }
            try:
                data = json.loads(message_content)
                check_point = data.get("check_point", "")
                check_result = data.get("check_result", "")
                original_document = data.get("original_document", "")
                
                logger.info(f"成功解析消息JSON数据，检查点：'{check_point}'")
                logger.debug(f"检查结果长度：{len(check_result)}字符")
                logger.debug(f"原始文档长度：{len(original_document)}字符")
                
                # 如果是纯文本消息，可能需要进行解析处理
                if not check_point and not check_result:
                    # 尝试从文本消息中提取信息
                    logger.info("尝试从文本消息中提取信息")
                    check_point = latest_msg.content
                    check_result = "已完成检查点评估"
                    original_document = ""
                    
                    # 尝试从消息历史中找到原始文档
                    doc_parsing_msgs = [msg for msg in self.rc.memory.get_by_role("DocumentParsingExpert") if "原始文档" in msg.content]
                    if doc_parsing_msgs:
                        try:
                            parsed_data = json.loads(doc_parsing_msgs[-1].content)
                            original_document = parsed_data.get("original_document", "")
                            logger.info("从历史消息中获取到原始文档")
                        except:
                            logger.warning("无法解析文档解析专家消息中的原始文档")
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试直接使用消息内容
                logger.warning("消息不是JSON格式，尝试直接使用消息内容")
                check_point = "当前检查点"
                check_result = latest_msg.content
                original_document = ""
                
                # 尝试从消息历史中找到原始文档
                doc_parsing_msgs = [msg for msg in self.rc.memory.get_by_role("DocumentParsingExpert")]
                if doc_parsing_msgs:
                    logger.info(f"找到{len(doc_parsing_msgs)}条文档解析专家消息")
                    for msg in reversed(doc_parsing_msgs):
                        try:
                            parsed_data = json.loads(msg.content)
                            if "original_document" in parsed_data:
                                original_document = parsed_data.get("original_document", "")
                                logger.info("从历史消息中获取到原始文档")
                                break
                        except:
                            continue
            
            # 检查是否获取到所有必要信息
            if not check_point or not check_result or not original_document:
                logger.warning(f"缺少必要信息进行验证: check_point={bool(check_point)}, check_result={bool(check_result)}, original_document={bool(original_document)}")
                return Message(
                    content="验证所需的信息不完整，无法进行逻辑一致性验证",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"DocumentCheckingExpert"},
                )
            
            # 执行逻辑一致性验证
            logger.info(f"开始执行逻辑一致性验证，检查点: '{check_point}'")
            verification_result = await todo.run(
                original_document=original_document,
                check_point=check_point,
                check_result=check_result
            )
            
            # 解析验证结果
            try:
                logger.info(f"验证结果: {verification_result[:100]}...")
                result_data = json.loads(verification_result)
                is_consistent = result_data.get("is_consistent", False)
                recommendation = result_data.get("recommendation", "")
                overall_assessment = result_data.get("overall_assessment", "")
                
                logger.info(f"逻辑一致性验证结果: is_consistent={is_consistent}")
                logger.info(f"验证建议: {recommendation}")
                
                # 记录验证结果
                self.verification_history.append({
                    "check_point": check_point,
                    "is_consistent": is_consistent,
                    "recommendation": recommendation
                })
                
                # 统计发现的问题数量
                hallucinations = result_data.get("hallucinations", [])
                reasoning_issues = result_data.get("reasoning_issues", [])
                format_matching_issues = result_data.get("format_matching_issues", [])
                conclusion_consistency = result_data.get("conclusion_consistency", True)
                
                logger.info(f"检测到的问题: 幻觉引用={len(hallucinations)}个, 推理问题={len(reasoning_issues)}个, 格式匹配问题={len(format_matching_issues)}个")
                logger.info(f"结论一致性: {conclusion_consistency}")
                
                # 根据验证结果处理后续流程
                if is_consistent:
                    # 如果逻辑一致，允许继续进行
                    logger.info(f"检查点 '{check_point}' 的评估结果与原始文档逻辑一致，验证通过")
                    return Message(
                        content=json.dumps({
                            "verification_result": "passed",
                            "check_point": check_point,
                            "assessment": overall_assessment
                        }),
                        role=self.profile,
                        cause_by=type(todo),
                        sent_from=self.name,
                        send_to={"DocumentSummaryExpert", "DocumentCheckingExpert"},
                    )
                else:
                    # 逻辑不一致，需要重新评估
                    logger.warning(f"检查点 '{check_point}' 的评估结果与原始文档逻辑不一致: {overall_assessment}")
                    
                    # 记录不同类型问题的检测结果
                    if hallucinations:
                        logger.warning(f"发现 {len(hallucinations)} 个引用内容问题")
                        for i, hall in enumerate(hallucinations):
                            logger.warning(f"  幻觉引用 {i+1}: 文本 '{hall.get('quoted_text', '')[:30]}...'")
                            logger.warning(f"    问题: {hall.get('issue', '')}")
                    
                    if reasoning_issues:
                        logger.warning(f"发现 {len(reasoning_issues)} 个推理过程问题")
                        for i, reason in enumerate(reasoning_issues):
                            logger.warning(f"  推理问题 {i+1}: 陈述 '{reason.get('statement', '')[:30]}...'")
                            logger.warning(f"    问题: {reason.get('issue', '')}")
                    
                    if format_matching_issues:
                        logger.warning(f"发现 {len(format_matching_issues)} 个强制匹配格式问题，可能存在过度解释或刻意分类")
                        for i, fmt_issue in enumerate(format_matching_issues):
                            logger.warning(f"  格式问题 {i+1}: 内容 '{fmt_issue.get('content', '')[:30]}...' 被不当分类为 '{fmt_issue.get('current_category', '')}'")
                            logger.warning(f"    问题: {fmt_issue.get('issue', '')}")
                    
                    # 构建详细的反馈信息
                    feedback = [f"对检查点 '{check_point}' 的评估存在逻辑不一致:"]
                    feedback.append(f"总体评估: {overall_assessment}")
                    
                    if hallucinations:
                        feedback.append("\n引用内容问题:")
                        for i, h in enumerate(hallucinations):
                            feedback.append(f"  {i+1}. 引用文本: \"{h.get('quoted_text', '')}\"")
                            feedback.append(f"     问题: {h.get('issue', '')}")
                            feedback.append(f"     修正建议: {h.get('correction', '')}")
                    
                    if reasoning_issues:
                        feedback.append("\n推理过程问题:")
                        for i, r in enumerate(reasoning_issues):
                            feedback.append(f"  {i+1}. 有问题的陈述: \"{r.get('statement', '')}\"")
                            feedback.append(f"     问题: {r.get('issue', '')}")
                            feedback.append(f"     修正建议: {r.get('correction', '')}")
                    
                    if format_matching_issues:
                        feedback.append("\n强制匹配格式问题:")
                        for i, f in enumerate(format_matching_issues):
                            feedback.append(f"  {i+1}. 不当分类内容: \"{f.get('content', '')}\"")
                            feedback.append(f"     当前分类: {f.get('current_category', '')}")
                            feedback.append(f"     问题: {f.get('issue', '')}")
                            feedback.append(f"     正确分类: {f.get('correct_category', '')}")
                    
                    feedback.append(f"\n建议: {recommendation}")
                    
                    logger.info("已生成详细反馈，发送回检查专家要求重新评估")
                    
                    # 将反馈发送回检查专家，要求重新评估
                    return Message(
                        content=json.dumps({
                            "verification_result": "failed",
                            "check_point": check_point,
                            "feedback": "\n".join(feedback),
                            "original_document": original_document,
                            "format_matching_issues_detected": len(format_matching_issues) > 0
                        }),
                        role=self.profile,
                        cause_by=type(todo),
                        sent_from=self.name,
                        send_to={"DocumentCheckingExpert"},
                    )
                
            except json.JSONDecodeError as e:
                logger.error(f"解析验证结果JSON时出错: {e}")
                logger.error(f"原始验证结果: {verification_result[:200]}...")
                return Message(
                    content=f"验证结果解析失败: {e}",
                    role=self.profile,
                    cause_by=type(todo),
                    sent_from=self.name,
                    send_to={"DocumentCheckingExpert"},
                )
            
        except Exception as e:
            logger.error(f"执行逻辑一致性验证时出错: {e}")
            import traceback
            error_trace = traceback.format_exc()
            logger.error(f"异常详情: {error_trace}")
            return Message(
                content=f"验证过程发生错误: {e}",
                role=self.profile,
                cause_by=type(todo),
                sent_from=self.name,
                send_to={"DocumentCheckingExpert"},
            ) 