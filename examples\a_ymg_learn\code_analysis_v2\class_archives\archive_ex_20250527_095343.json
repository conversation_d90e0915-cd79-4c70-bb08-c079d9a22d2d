{"classes": {"SimpleWriteCode": {"name": "SimpleWriteCode", "file_path": "d:\\bigModel\\curser\\stock_test1\\backend\\MetaGPT\\examples\\a_ymg_learn\\ex.py", "start_line": 38, "end_line": 53, "bases": ["Action"], "decorators": [], "docstring": null, "methods": {"run": {"name": "run", "start_line": 46, "end_line": 53, "is_async": true, "args": ["self", "instruction"], "decorators": [], "docstring": null, "source_code": "    async def run(self, instruction: str):\n        prompt = self.PROMPT_TEMPLATE.format(instruction=instruction)\n\n        rsp = await self._aask(prompt)\n\n        code_text = parse_code(rsp)\n\n        return code_text", "ast_dump": "AsyncFunctionDef(\n  name='run',\n  args=arguments(\n    posonlyargs=[],\n    args=[\n      arg(arg='self'),\n      arg(\n        arg='instruction',\n        annotation=Name(id='str', ctx=Load()))],\n    kwonlyargs=[],\n    kw_defaults=[],\n    defaults=[]),\n  body=[\n    Assign(\n      targets=[\n        Name(id='prompt', ctx=Store())],\n      value=Call(\n        func=Attribute(\n          value=Attribute(\n            value=Name(id='self', ctx=Load()),\n            attr='PROMPT_TEMPLATE',\n            ctx=Load()),\n          attr='format',\n          ctx=Load()),\n        args=[],\n        keywords=[\n          keyword(\n            arg='instruction',\n            value=Name(id='instruction', ctx=Load()))])),\n    Assign(\n      targets=[\n        Name(id='rsp', ctx=Store())],\n      value=Await(\n        value=Call(\n          func=Attribute(\n            value=Name(id='self', ctx=Load()),\n            attr='_aask',\n            ctx=Load()),\n          args=[\n            Name(id='prompt', ctx=Load())],\n          keywords=[]))),\n    Assign(\n      targets=[\n        Name(id='code_text', ctx=Store())],\n      value=Call(\n        func=Name(id='parse_code', ctx=Load()),\n        args=[\n          Name(id='rsp', ctx=Load())],\n        keywords=[])),\n    Return(\n      value=Name(id='code_text', ctx=Load()))],\n  decorator_list=[])", "returns": null}}, "attributes": [], "method_calls": [{"caller_method": "SimpleWriteCode.run", "called_method": "parse_code", "call_line": 51, "call_type": "static", "target_class": null}, {"caller_method": "SimpleWriteCode.run", "called_method": "_aask", "call_line": 49, "call_type": "self", "target_class": "SimpleWriteCode"}], "ast_dump": "ClassDef(\n  name='SimpleWriteCode',\n  bases=[\n    Name(id='Action', ctx=Load())],\n  keywords=[],\n  body=[\n    AnnAssign(\n      target=Name(id='PROMPT_TEMPLATE', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='\\n    Write a python function that can {instruction}.\\n    Return ```python your_code_here ``` with NO other texts,\\n    your code:\\n    '),\n      simple=1),\n    AnnAssign(\n      target=Name(id='name', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='SimpleWriteCode'),\n      simple=1),\n    AsyncFunctionDef(\n      name='run',\n      args=arguments(\n        posonlyargs=[],\n        args=[\n          arg(arg='self'),\n          arg(\n            arg='instruction',\n            annotation=Name(id='str', ctx=Load()))],\n        kwonlyargs=[],\n        kw_defaults=[],\n        defaults=[]),\n      body=[\n        Assign(\n          targets=[\n            Name(id='prompt', ctx=Store())],\n          value=Call(\n            func=Attribute(\n              value=Attribute(\n                value=Name(id='self', ctx=Load()),\n                attr='PROMPT_TEMPLATE',\n                ctx=Load()),\n              attr='format',\n              ctx=Load()),\n            args=[],\n            keywords=[\n              keyword(\n                arg='instruction',\n                value=Name(id='instruction', ctx=Load()))])),\n        Assign(\n          targets=[\n            Name(id='rsp', ctx=Store())],\n          value=Await(\n            value=Call(\n              func=Attribute(\n                value=Name(id='self', ctx=Load()),\n                attr='_aask',\n                ctx=Load()),\n              args=[\n                Name(id='prompt', ctx=Load())],\n              keywords=[]))),\n        Assign(\n          targets=[\n            Name(id='code_text', ctx=Store())],\n          value=Call(\n            func=Name(id='parse_code', ctx=Load()),\n            args=[\n              Name(id='rsp', ctx=Load())],\n            keywords=[])),\n        Return(\n          value=Name(id='code_text', ctx=Load()))],\n      decorator_list=[])],\n  decorator_list=[])", "source_code": "class SimpleWriteCode(Action):\n    PROMPT_TEMPLATE: str = \"\"\"\n    Write a python function that can {instruction}.\n    Return ```python your_code_here ``` with NO other texts,\n    your code:\n    \"\"\"\n    name: str = \"SimpleWriteCode\"\n\n    async def run(self, instruction: str):\n        prompt = self.PROMPT_TEMPLATE.format(instruction=instruction)\n\n        rsp = await self._aask(prompt)\n\n        code_text = parse_code(rsp)\n\n        return code_text", "hash_value": "3da7e0672726062b88ac3ee2f2273967", "analysis_result": "分析出错: <PERSON><PERSON><PERSON> communicating with LLM", "last_updated": "2025-05-27T09:53:30.714523"}, "SimpleCoder": {"name": "SimpleCoder", "file_path": "d:\\bigModel\\curser\\stock_test1\\backend\\MetaGPT\\examples\\a_ymg_learn\\ex.py", "start_line": 56, "end_line": 63, "bases": ["Role"], "decorators": [], "docstring": null, "methods": {"__init__": {"name": "__init__", "start_line": 60, "end_line": 63, "is_async": false, "args": ["self"], "decorators": [], "docstring": null, "source_code": "    def __init__(self, **kwargs):\n        super().__init__(**kwargs)\n        self._watch([UserRequirement])\n        self.set_actions([SimpleWriteCode])", "ast_dump": "FunctionDef(\n  name='__init__',\n  args=arguments(\n    posonlyargs=[],\n    args=[\n      arg(arg='self')],\n    kwonlyargs=[],\n    kw_defaults=[],\n    kwarg=arg(arg='kwargs'),\n    defaults=[]),\n  body=[\n    Expr(\n      value=Call(\n        func=Attribute(\n          value=Call(\n            func=Name(id='super', ctx=Load()),\n            args=[],\n            keywords=[]),\n          attr='__init__',\n          ctx=Load()),\n        args=[],\n        keywords=[\n          keyword(\n            value=Name(id='kwargs', ctx=Load()))])),\n    Expr(\n      value=Call(\n        func=Attribute(\n          value=Name(id='self', ctx=Load()),\n          attr='_watch',\n          ctx=Load()),\n        args=[\n          List(\n            elts=[\n              Name(id='UserRequirement', ctx=Load())],\n            ctx=Load())],\n        keywords=[])),\n    Expr(\n      value=Call(\n        func=Attribute(\n          value=Name(id='self', ctx=Load()),\n          attr='set_actions',\n          ctx=Load()),\n        args=[\n          List(\n            elts=[\n              Name(id='SimpleWriteCode', ctx=Load())],\n            ctx=Load())],\n        keywords=[]))],\n  decorator_list=[])", "returns": null}}, "attributes": [], "method_calls": [{"caller_method": "SimpleCoder.__init__", "called_method": "_watch", "call_line": 62, "call_type": "self", "target_class": "SimpleCoder"}, {"caller_method": "SimpleCoder.__init__", "called_method": "set_actions", "call_line": 63, "call_type": "self", "target_class": "SimpleCoder"}, {"caller_method": "SimpleCoder.__init__", "called_method": "super", "call_line": 61, "call_type": "static", "target_class": null}], "ast_dump": "ClassDef(\n  name='SimpleCoder',\n  bases=[\n    Name(id='Role', ctx=Load())],\n  keywords=[],\n  body=[\n    AnnAssign(\n      target=Name(id='name', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='Alice'),\n      simple=1),\n    AnnAssign(\n      target=Name(id='profile', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='SimpleCoder'),\n      simple=1),\n    FunctionDef(\n      name='__init__',\n      args=arguments(\n        posonlyargs=[],\n        args=[\n          arg(arg='self')],\n        kwonlyargs=[],\n        kw_defaults=[],\n        kwarg=arg(arg='kwargs'),\n        defaults=[]),\n      body=[\n        Expr(\n          value=Call(\n            func=Attribute(\n              value=Call(\n                func=Name(id='super', ctx=Load()),\n                args=[],\n                keywords=[]),\n              attr='__init__',\n              ctx=Load()),\n            args=[],\n            keywords=[\n              keyword(\n                value=Name(id='kwargs', ctx=Load()))])),\n        Expr(\n          value=Call(\n            func=Attribute(\n              value=Name(id='self', ctx=Load()),\n              attr='_watch',\n              ctx=Load()),\n            args=[\n              List(\n                elts=[\n                  Name(id='UserRequirement', ctx=Load())],\n                ctx=Load())],\n            keywords=[])),\n        Expr(\n          value=Call(\n            func=Attribute(\n              value=Name(id='self', ctx=Load()),\n              attr='set_actions',\n              ctx=Load()),\n            args=[\n              List(\n                elts=[\n                  Name(id='SimpleWriteCode', ctx=Load())],\n                ctx=Load())],\n            keywords=[]))],\n      decorator_list=[])],\n  decorator_list=[])", "source_code": "class SimpleCoder(Role):\n    name: str = \"Alice\"\n    profile: str = \"SimpleCoder\"\n\n    def __init__(self, **kwargs):\n        super().__init__(**kwargs)\n        self._watch([UserRequirement])\n        self.set_actions([SimpleWriteCode])", "hash_value": "9f93cc0ac3680ae59e4a4b45d6065140", "analysis_result": "分析出错: <PERSON><PERSON><PERSON> communicating with LLM", "last_updated": "2025-05-27T09:53:30.714523"}, "SimpleWriteTest": {"name": "SimpleWriteTest", "file_path": "d:\\bigModel\\curser\\stock_test1\\backend\\MetaGPT\\examples\\a_ymg_learn\\ex.py", "start_line": 66, "end_line": 83, "bases": ["Action"], "decorators": [], "docstring": null, "methods": {"run": {"name": "run", "start_line": 76, "end_line": 83, "is_async": true, "args": ["self", "context", "k"], "decorators": [], "docstring": null, "source_code": "    async def run(self, context: str, k: int = 3):\n        prompt = self.PROMPT_TEMPLATE.format(context=context, k=k)\n\n        rsp = await self._aask(prompt)\n\n        code_text = parse_code(rsp)\n\n        return code_text", "ast_dump": "AsyncFunctionDef(\n  name='run',\n  args=arguments(\n    posonlyargs=[],\n    args=[\n      arg(arg='self'),\n      arg(\n        arg='context',\n        annotation=Name(id='str', ctx=Load())),\n      arg(\n        arg='k',\n        annotation=Name(id='int', ctx=Load()))],\n    kwonlyargs=[],\n    kw_defaults=[],\n    defaults=[\n      Constant(value=3)]),\n  body=[\n    Assign(\n      targets=[\n        Name(id='prompt', ctx=Store())],\n      value=Call(\n        func=Attribute(\n          value=Attribute(\n            value=Name(id='self', ctx=Load()),\n            attr='PROMPT_TEMPLATE',\n            ctx=Load()),\n          attr='format',\n          ctx=Load()),\n        args=[],\n        keywords=[\n          keyword(\n            arg='context',\n            value=Name(id='context', ctx=Load())),\n          keyword(\n            arg='k',\n            value=Name(id='k', ctx=Load()))])),\n    Assign(\n      targets=[\n        Name(id='rsp', ctx=Store())],\n      value=Await(\n        value=Call(\n          func=Attribute(\n            value=Name(id='self', ctx=Load()),\n            attr='_aask',\n            ctx=Load()),\n          args=[\n            Name(id='prompt', ctx=Load())],\n          keywords=[]))),\n    Assign(\n      targets=[\n        Name(id='code_text', ctx=Store())],\n      value=Call(\n        func=Name(id='parse_code', ctx=Load()),\n        args=[\n          Name(id='rsp', ctx=Load())],\n        keywords=[])),\n    Return(\n      value=Name(id='code_text', ctx=Load()))],\n  decorator_list=[])", "returns": null}}, "attributes": [], "method_calls": [{"caller_method": "SimpleWriteTest.run", "called_method": "parse_code", "call_line": 81, "call_type": "static", "target_class": null}, {"caller_method": "SimpleWriteTest.run", "called_method": "_aask", "call_line": 79, "call_type": "self", "target_class": "SimpleWriteTest"}], "ast_dump": "ClassDef(\n  name='SimpleWriteTest',\n  bases=[\n    Name(id='Action', ctx=Load())],\n  keywords=[],\n  body=[\n    AnnAssign(\n      target=Name(id='PROMPT_TEMPLATE', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='\\n    Context: {context}\\n    Write {k} unit tests using pytest for the given function, assuming you have imported it.\\n    Return ```python your_code_here ``` with NO other texts,\\n    your code:\\n    '),\n      simple=1),\n    AnnAssign(\n      target=Name(id='name', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='SimpleWriteTest'),\n      simple=1),\n    AsyncFunctionDef(\n      name='run',\n      args=arguments(\n        posonlyargs=[],\n        args=[\n          arg(arg='self'),\n          arg(\n            arg='context',\n            annotation=Name(id='str', ctx=Load())),\n          arg(\n            arg='k',\n            annotation=Name(id='int', ctx=Load()))],\n        kwonlyargs=[],\n        kw_defaults=[],\n        defaults=[\n          Constant(value=3)]),\n      body=[\n        Assign(\n          targets=[\n            Name(id='prompt', ctx=Store())],\n          value=Call(\n            func=Attribute(\n              value=Attribute(\n                value=Name(id='self', ctx=Load()),\n                attr='PROMPT_TEMPLATE',\n                ctx=Load()),\n              attr='format',\n              ctx=Load()),\n            args=[],\n            keywords=[\n              keyword(\n                arg='context',\n                value=Name(id='context', ctx=Load())),\n              keyword(\n                arg='k',\n                value=Name(id='k', ctx=Load()))])),\n        Assign(\n          targets=[\n            Name(id='rsp', ctx=Store())],\n          value=Await(\n            value=Call(\n              func=Attribute(\n                value=Name(id='self', ctx=Load()),\n                attr='_aask',\n                ctx=Load()),\n              args=[\n                Name(id='prompt', ctx=Load())],\n              keywords=[]))),\n        Assign(\n          targets=[\n            Name(id='code_text', ctx=Store())],\n          value=Call(\n            func=Name(id='parse_code', ctx=Load()),\n            args=[\n              Name(id='rsp', ctx=Load())],\n            keywords=[])),\n        Return(\n          value=Name(id='code_text', ctx=Load()))],\n      decorator_list=[])],\n  decorator_list=[])", "source_code": "class SimpleWriteTest(Action):\n    PROMPT_TEMPLATE: str = \"\"\"\n    Context: {context}\n    Write {k} unit tests using pytest for the given function, assuming you have imported it.\n    Return ```python your_code_here ``` with NO other texts,\n    your code:\n    \"\"\"\n\n    name: str = \"SimpleWriteTest\"\n\n    async def run(self, context: str, k: int = 3):\n        prompt = self.PROMPT_TEMPLATE.format(context=context, k=k)\n\n        rsp = await self._aask(prompt)\n\n        code_text = parse_code(rsp)\n\n        return code_text", "hash_value": "78c66533a9c66de1b29d61c9c57c3b3a", "analysis_result": "分析出错: <PERSON><PERSON><PERSON> communicating with LLM", "last_updated": "2025-05-27T09:53:30.715522"}, "SimpleTester": {"name": "SimpleTester", "file_path": "d:\\bigModel\\curser\\stock_test1\\backend\\MetaGPT\\examples\\a_ymg_learn\\ex.py", "start_line": 86, "end_line": 106, "bases": ["Role"], "decorators": [], "docstring": null, "methods": {"__init__": {"name": "__init__", "start_line": 90, "end_line": 94, "is_async": false, "args": ["self"], "decorators": [], "docstring": null, "source_code": "    def __init__(self, **kwargs):\n        super().__init__(**kwargs)\n        self.set_actions([SimpleWriteTest])\n        # self._watch([SimpleWriteCode])\n        self._watch([SimpleWriteCode, SimpleWriteReview])  # feel free to try this too", "ast_dump": "FunctionDef(\n  name='__init__',\n  args=arguments(\n    posonlyargs=[],\n    args=[\n      arg(arg='self')],\n    kwonlyargs=[],\n    kw_defaults=[],\n    kwarg=arg(arg='kwargs'),\n    defaults=[]),\n  body=[\n    Expr(\n      value=Call(\n        func=Attribute(\n          value=Call(\n            func=Name(id='super', ctx=Load()),\n            args=[],\n            keywords=[]),\n          attr='__init__',\n          ctx=Load()),\n        args=[],\n        keywords=[\n          keyword(\n            value=Name(id='kwargs', ctx=Load()))])),\n    Expr(\n      value=Call(\n        func=Attribute(\n          value=Name(id='self', ctx=Load()),\n          attr='set_actions',\n          ctx=Load()),\n        args=[\n          List(\n            elts=[\n              Name(id='SimpleWriteTest', ctx=Load())],\n            ctx=Load())],\n        keywords=[])),\n    Expr(\n      value=Call(\n        func=Attribute(\n          value=Name(id='self', ctx=Load()),\n          attr='_watch',\n          ctx=Load()),\n        args=[\n          List(\n            elts=[\n              Name(id='SimpleWriteCode', ctx=Load()),\n              Name(id='SimpleWriteReview', ctx=Load())],\n            ctx=Load())],\n        keywords=[]))],\n  decorator_list=[])", "returns": null}, "_act": {"name": "_act", "start_line": 96, "end_line": 106, "is_async": true, "args": ["self"], "decorators": [], "docstring": null, "source_code": "    async def _act(self) -> Message:\n        logger.info(f\"{self._setting}: to do {self.rc.todo}({self.rc.todo.name})\")\n        todo = self.rc.todo\n\n        # context = self.get_memories(k=1)[0].content # use the most recent memory as context\n        context = self.get_memories()  # use all memories as context\n\n        code_text = await todo.run(context, k=5)  # specify arguments\n        msg = Message(content=code_text, role=self.profile, cause_by=type(todo))\n\n        return msg", "ast_dump": "AsyncFunctionDef(\n  name='_act',\n  args=arguments(\n    posonlyargs=[],\n    args=[\n      arg(arg='self')],\n    kwonlyargs=[],\n    kw_defaults=[],\n    defaults=[]),\n  body=[\n    Expr(\n      value=Call(\n        func=Attribute(\n          value=Name(id='logger', ctx=Load()),\n          attr='info',\n          ctx=Load()),\n        args=[\n          JoinedStr(\n            values=[\n              FormattedValue(\n                value=Attribute(\n                  value=Name(id='self', ctx=Load()),\n                  attr='_setting',\n                  ctx=Load()),\n                conversion=-1),\n              Constant(value=': to do '),\n              FormattedValue(\n                value=Attribute(\n                  value=Attribute(\n                    value=Name(id='self', ctx=Load()),\n                    attr='rc',\n                    ctx=Load()),\n                  attr='todo',\n                  ctx=Load()),\n                conversion=-1),\n              Constant(value='('),\n              FormattedValue(\n                value=Attribute(\n                  value=Attribute(\n                    value=Attribute(\n                      value=Name(id='self', ctx=Load()),\n                      attr='rc',\n                      ctx=Load()),\n                    attr='todo',\n                    ctx=Load()),\n                  attr='name',\n                  ctx=Load()),\n                conversion=-1),\n              Constant(value=')')])],\n        keywords=[])),\n    Assign(\n      targets=[\n        Name(id='todo', ctx=Store())],\n      value=Attribute(\n        value=Attribute(\n          value=Name(id='self', ctx=Load()),\n          attr='rc',\n          ctx=Load()),\n        attr='todo',\n        ctx=Load())),\n    Assign(\n      targets=[\n        Name(id='context', ctx=Store())],\n      value=Call(\n        func=Attribute(\n          value=Name(id='self', ctx=Load()),\n          attr='get_memories',\n          ctx=Load()),\n        args=[],\n        keywords=[])),\n    Assign(\n      targets=[\n        Name(id='code_text', ctx=Store())],\n      value=Await(\n        value=Call(\n          func=Attribute(\n            value=Name(id='todo', ctx=Load()),\n            attr='run',\n            ctx=Load()),\n          args=[\n            Name(id='context', ctx=Load())],\n          keywords=[\n            keyword(\n              arg='k',\n              value=Constant(value=5))]))),\n    Assign(\n      targets=[\n        Name(id='msg', ctx=Store())],\n      value=Call(\n        func=Name(id='Message', ctx=Load()),\n        args=[],\n        keywords=[\n          keyword(\n            arg='content',\n            value=Name(id='code_text', ctx=Load())),\n          keyword(\n            arg='role',\n            value=Attribute(\n              value=Name(id='self', ctx=Load()),\n              attr='profile',\n              ctx=Load())),\n          keyword(\n            arg='cause_by',\n            value=Call(\n              func=Name(id='type', ctx=Load()),\n              args=[\n                Name(id='todo', ctx=Load())],\n              keywords=[]))])),\n    Return(\n      value=Name(id='msg', ctx=Load()))],\n  decorator_list=[],\n  returns=Name(id='Message', ctx=Load()))", "returns": "Message"}}, "attributes": [], "method_calls": [{"caller_method": "SimpleTester.__init__", "called_method": "set_actions", "call_line": 92, "call_type": "self", "target_class": "SimpleTester"}, {"caller_method": "SimpleTester.__init__", "called_method": "_watch", "call_line": 94, "call_type": "self", "target_class": "SimpleTester"}, {"caller_method": "SimpleTester.__init__", "called_method": "super", "call_line": 91, "call_type": "static", "target_class": null}, {"caller_method": "SimpleTester._act", "called_method": "info", "call_line": 97, "call_type": "external", "target_class": "logger"}, {"caller_method": "SimpleTester._act", "called_method": "get_memories", "call_line": 101, "call_type": "self", "target_class": "SimpleTester"}, {"caller_method": "SimpleTester._act", "called_method": "Message", "call_line": 104, "call_type": "static", "target_class": null}, {"caller_method": "SimpleTester._act", "called_method": "run", "call_line": 103, "call_type": "external", "target_class": "todo"}, {"caller_method": "SimpleTester._act", "called_method": "type", "call_line": 104, "call_type": "static", "target_class": null}], "ast_dump": "ClassDef(\n  name='SimpleTester',\n  bases=[\n    Name(id='Role', ctx=Load())],\n  keywords=[],\n  body=[\n    AnnAssign(\n      target=Name(id='name', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='Bob'),\n      simple=1),\n    AnnAssign(\n      target=Name(id='profile', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='SimpleTester'),\n      simple=1),\n    FunctionDef(\n      name='__init__',\n      args=arguments(\n        posonlyargs=[],\n        args=[\n          arg(arg='self')],\n        kwonlyargs=[],\n        kw_defaults=[],\n        kwarg=arg(arg='kwargs'),\n        defaults=[]),\n      body=[\n        Expr(\n          value=Call(\n            func=Attribute(\n              value=Call(\n                func=Name(id='super', ctx=Load()),\n                args=[],\n                keywords=[]),\n              attr='__init__',\n              ctx=Load()),\n            args=[],\n            keywords=[\n              keyword(\n                value=Name(id='kwargs', ctx=Load()))])),\n        Expr(\n          value=Call(\n            func=Attribute(\n              value=Name(id='self', ctx=Load()),\n              attr='set_actions',\n              ctx=Load()),\n            args=[\n              List(\n                elts=[\n                  Name(id='SimpleWriteTest', ctx=Load())],\n                ctx=Load())],\n            keywords=[])),\n        Expr(\n          value=Call(\n            func=Attribute(\n              value=Name(id='self', ctx=Load()),\n              attr='_watch',\n              ctx=Load()),\n            args=[\n              List(\n                elts=[\n                  Name(id='SimpleWriteCode', ctx=Load()),\n                  Name(id='SimpleWriteReview', ctx=Load())],\n                ctx=Load())],\n            keywords=[]))],\n      decorator_list=[]),\n    AsyncFunctionDef(\n      name='_act',\n      args=arguments(\n        posonlyargs=[],\n        args=[\n          arg(arg='self')],\n        kwonlyargs=[],\n        kw_defaults=[],\n        defaults=[]),\n      body=[\n        Expr(\n          value=Call(\n            func=Attribute(\n              value=Name(id='logger', ctx=Load()),\n              attr='info',\n              ctx=Load()),\n            args=[\n              JoinedStr(\n                values=[\n                  FormattedValue(\n                    value=Attribute(\n                      value=Name(id='self', ctx=Load()),\n                      attr='_setting',\n                      ctx=Load()),\n                    conversion=-1),\n                  Constant(value=': to do '),\n                  FormattedValue(\n                    value=Attribute(\n                      value=Attribute(\n                        value=Name(id='self', ctx=Load()),\n                        attr='rc',\n                        ctx=Load()),\n                      attr='todo',\n                      ctx=Load()),\n                    conversion=-1),\n                  Constant(value='('),\n                  FormattedValue(\n                    value=Attribute(\n                      value=Attribute(\n                        value=Attribute(\n                          value=Name(id='self', ctx=Load()),\n                          attr='rc',\n                          ctx=Load()),\n                        attr='todo',\n                        ctx=Load()),\n                      attr='name',\n                      ctx=Load()),\n                    conversion=-1),\n                  Constant(value=')')])],\n            keywords=[])),\n        Assign(\n          targets=[\n            Name(id='todo', ctx=Store())],\n          value=Attribute(\n            value=Attribute(\n              value=Name(id='self', ctx=Load()),\n              attr='rc',\n              ctx=Load()),\n            attr='todo',\n            ctx=Load())),\n        Assign(\n          targets=[\n            Name(id='context', ctx=Store())],\n          value=Call(\n            func=Attribute(\n              value=Name(id='self', ctx=Load()),\n              attr='get_memories',\n              ctx=Load()),\n            args=[],\n            keywords=[])),\n        Assign(\n          targets=[\n            Name(id='code_text', ctx=Store())],\n          value=Await(\n            value=Call(\n              func=Attribute(\n                value=Name(id='todo', ctx=Load()),\n                attr='run',\n                ctx=Load()),\n              args=[\n                Name(id='context', ctx=Load())],\n              keywords=[\n                keyword(\n                  arg='k',\n                  value=Constant(value=5))]))),\n        Assign(\n          targets=[\n            Name(id='msg', ctx=Store())],\n          value=Call(\n            func=Name(id='Message', ctx=Load()),\n            args=[],\n            keywords=[\n              keyword(\n                arg='content',\n                value=Name(id='code_text', ctx=Load())),\n              keyword(\n                arg='role',\n                value=Attribute(\n                  value=Name(id='self', ctx=Load()),\n                  attr='profile',\n                  ctx=Load())),\n              keyword(\n                arg='cause_by',\n                value=Call(\n                  func=Name(id='type', ctx=Load()),\n                  args=[\n                    Name(id='todo', ctx=Load())],\n                  keywords=[]))])),\n        Return(\n          value=Name(id='msg', ctx=Load()))],\n      decorator_list=[],\n      returns=Name(id='Message', ctx=Load()))],\n  decorator_list=[])", "source_code": "class SimpleTester(Role):\n    name: str = \"Bob\"\n    profile: str = \"SimpleTester\"\n\n    def __init__(self, **kwargs):\n        super().__init__(**kwargs)\n        self.set_actions([SimpleWriteTest])\n        # self._watch([SimpleWriteCode])\n        self._watch([SimpleWriteCode, SimpleWriteReview])  # feel free to try this too\n\n    async def _act(self) -> Message:\n        logger.info(f\"{self._setting}: to do {self.rc.todo}({self.rc.todo.name})\")\n        todo = self.rc.todo\n\n        # context = self.get_memories(k=1)[0].content # use the most recent memory as context\n        context = self.get_memories()  # use all memories as context\n\n        code_text = await todo.run(context, k=5)  # specify arguments\n        msg = Message(content=code_text, role=self.profile, cause_by=type(todo))\n\n        return msg", "hash_value": "b0cd1a5fd83b740bcd42501120f39d3d", "analysis_result": "分析出错: <PERSON><PERSON><PERSON> communicating with LLM", "last_updated": "2025-05-27T09:53:30.715522"}, "SimpleWriteReview": {"name": "SimpleWriteReview", "file_path": "d:\\bigModel\\curser\\stock_test1\\backend\\MetaGPT\\examples\\a_ymg_learn\\ex.py", "start_line": 109, "end_line": 122, "bases": ["Action"], "decorators": [], "docstring": null, "methods": {"run": {"name": "run", "start_line": 117, "end_line": 122, "is_async": true, "args": ["self", "context"], "decorators": [], "docstring": null, "source_code": "    async def run(self, context: str):\n        prompt = self.PROMPT_TEMPLATE.format(context=context)\n\n        rsp = await self._aask(prompt)\n\n        return rsp", "ast_dump": "AsyncFunctionDef(\n  name='run',\n  args=arguments(\n    posonlyargs=[],\n    args=[\n      arg(arg='self'),\n      arg(\n        arg='context',\n        annotation=Name(id='str', ctx=Load()))],\n    kwonlyargs=[],\n    kw_defaults=[],\n    defaults=[]),\n  body=[\n    Assign(\n      targets=[\n        Name(id='prompt', ctx=Store())],\n      value=Call(\n        func=Attribute(\n          value=Attribute(\n            value=Name(id='self', ctx=Load()),\n            attr='PROMPT_TEMPLATE',\n            ctx=Load()),\n          attr='format',\n          ctx=Load()),\n        args=[],\n        keywords=[\n          keyword(\n            arg='context',\n            value=Name(id='context', ctx=Load()))])),\n    Assign(\n      targets=[\n        Name(id='rsp', ctx=Store())],\n      value=Await(\n        value=Call(\n          func=Attribute(\n            value=Name(id='self', ctx=Load()),\n            attr='_aask',\n            ctx=Load()),\n          args=[\n            Name(id='prompt', ctx=Load())],\n          keywords=[]))),\n    Return(\n      value=Name(id='rsp', ctx=Load()))],\n  decorator_list=[])", "returns": null}}, "attributes": [], "method_calls": [{"caller_method": "SimpleWriteReview.run", "called_method": "_aask", "call_line": 120, "call_type": "self", "target_class": "SimpleWriteReview"}], "ast_dump": "ClassDef(\n  name='SimpleWriteReview',\n  bases=[\n    Name(id='Action', ctx=Load())],\n  keywords=[],\n  body=[\n    AnnAssign(\n      target=Name(id='PROMPT_TEMPLATE', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='\\n    Context: {context}\\n    Review the test cases and provide one critical comments:\\n    '),\n      simple=1),\n    AnnAssign(\n      target=Name(id='name', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='SimpleWriteReview'),\n      simple=1),\n    AsyncFunctionDef(\n      name='run',\n      args=arguments(\n        posonlyargs=[],\n        args=[\n          arg(arg='self'),\n          arg(\n            arg='context',\n            annotation=Name(id='str', ctx=Load()))],\n        kwonlyargs=[],\n        kw_defaults=[],\n        defaults=[]),\n      body=[\n        Assign(\n          targets=[\n            Name(id='prompt', ctx=Store())],\n          value=Call(\n            func=Attribute(\n              value=Attribute(\n                value=Name(id='self', ctx=Load()),\n                attr='PROMPT_TEMPLATE',\n                ctx=Load()),\n              attr='format',\n              ctx=Load()),\n            args=[],\n            keywords=[\n              keyword(\n                arg='context',\n                value=Name(id='context', ctx=Load()))])),\n        Assign(\n          targets=[\n            Name(id='rsp', ctx=Store())],\n          value=Await(\n            value=Call(\n              func=Attribute(\n                value=Name(id='self', ctx=Load()),\n                attr='_aask',\n                ctx=Load()),\n              args=[\n                Name(id='prompt', ctx=Load())],\n              keywords=[]))),\n        Return(\n          value=Name(id='rsp', ctx=Load()))],\n      decorator_list=[])],\n  decorator_list=[])", "source_code": "class SimpleWriteReview(Action):\n    PROMPT_TEMPLATE: str = \"\"\"\n    Context: {context}\n    Review the test cases and provide one critical comments:\n    \"\"\"\n\n    name: str = \"SimpleWriteReview\"\n\n    async def run(self, context: str):\n        prompt = self.PROMPT_TEMPLATE.format(context=context)\n\n        rsp = await self._aask(prompt)\n\n        return rsp", "hash_value": "a29199883fc275567a142a3d10661ab9", "analysis_result": "分析出错: <PERSON><PERSON><PERSON> communicating with LLM", "last_updated": "2025-05-27T09:53:30.716522"}, "SimpleReviewer": {"name": "SimpleReviewer", "file_path": "d:\\bigModel\\curser\\stock_test1\\backend\\MetaGPT\\examples\\a_ymg_learn\\ex.py", "start_line": 125, "end_line": 132, "bases": ["Role"], "decorators": [], "docstring": null, "methods": {"__init__": {"name": "__init__", "start_line": 129, "end_line": 132, "is_async": false, "args": ["self"], "decorators": [], "docstring": null, "source_code": "    def __init__(self, **kwargs):\n        super().__init__(**kwargs)\n        self.set_actions([SimpleWriteReview])\n        self._watch([SimpleWriteTest])", "ast_dump": "FunctionDef(\n  name='__init__',\n  args=arguments(\n    posonlyargs=[],\n    args=[\n      arg(arg='self')],\n    kwonlyargs=[],\n    kw_defaults=[],\n    kwarg=arg(arg='kwargs'),\n    defaults=[]),\n  body=[\n    Expr(\n      value=Call(\n        func=Attribute(\n          value=Call(\n            func=Name(id='super', ctx=Load()),\n            args=[],\n            keywords=[]),\n          attr='__init__',\n          ctx=Load()),\n        args=[],\n        keywords=[\n          keyword(\n            value=Name(id='kwargs', ctx=Load()))])),\n    Expr(\n      value=Call(\n        func=Attribute(\n          value=Name(id='self', ctx=Load()),\n          attr='set_actions',\n          ctx=Load()),\n        args=[\n          List(\n            elts=[\n              Name(id='SimpleWriteReview', ctx=Load())],\n            ctx=Load())],\n        keywords=[])),\n    Expr(\n      value=Call(\n        func=Attribute(\n          value=Name(id='self', ctx=Load()),\n          attr='_watch',\n          ctx=Load()),\n        args=[\n          List(\n            elts=[\n              Name(id='SimpleWriteTest', ctx=Load())],\n            ctx=Load())],\n        keywords=[]))],\n  decorator_list=[])", "returns": null}}, "attributes": [], "method_calls": [{"caller_method": "SimpleReviewer.__init__", "called_method": "set_actions", "call_line": 131, "call_type": "self", "target_class": "SimpleReviewer"}, {"caller_method": "SimpleReviewer.__init__", "called_method": "_watch", "call_line": 132, "call_type": "self", "target_class": "SimpleReviewer"}, {"caller_method": "SimpleReviewer.__init__", "called_method": "super", "call_line": 130, "call_type": "static", "target_class": null}], "ast_dump": "ClassDef(\n  name='SimpleReviewer',\n  bases=[\n    Name(id='Role', ctx=Load())],\n  keywords=[],\n  body=[\n    AnnAssign(\n      target=Name(id='name', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='Charlie'),\n      simple=1),\n    AnnAssign(\n      target=Name(id='profile', ctx=Store()),\n      annotation=Name(id='str', ctx=Load()),\n      value=Constant(value='SimpleReviewer'),\n      simple=1),\n    FunctionDef(\n      name='__init__',\n      args=arguments(\n        posonlyargs=[],\n        args=[\n          arg(arg='self')],\n        kwonlyargs=[],\n        kw_defaults=[],\n        kwarg=arg(arg='kwargs'),\n        defaults=[]),\n      body=[\n        Expr(\n          value=Call(\n            func=Attribute(\n              value=Call(\n                func=Name(id='super', ctx=Load()),\n                args=[],\n                keywords=[]),\n              attr='__init__',\n              ctx=Load()),\n            args=[],\n            keywords=[\n              keyword(\n                value=Name(id='kwargs', ctx=Load()))])),\n        Expr(\n          value=Call(\n            func=Attribute(\n              value=Name(id='self', ctx=Load()),\n              attr='set_actions',\n              ctx=Load()),\n            args=[\n              List(\n                elts=[\n                  Name(id='SimpleWriteReview', ctx=Load())],\n                ctx=Load())],\n            keywords=[])),\n        Expr(\n          value=Call(\n            func=Attribute(\n              value=Name(id='self', ctx=Load()),\n              attr='_watch',\n              ctx=Load()),\n            args=[\n              List(\n                elts=[\n                  Name(id='SimpleWriteTest', ctx=Load())],\n                ctx=Load())],\n            keywords=[]))],\n      decorator_list=[])],\n  decorator_list=[])", "source_code": "class SimpleReviewer(Role):\n    name: str = \"<PERSON>\"\n    profile: str = \"SimpleReviewer\"\n\n    def __init__(self, **kwargs):\n        super().__init__(**kwargs)\n        self.set_actions([SimpleWriteReview])\n        self._watch([SimpleWriteTest])", "hash_value": "f4b2eb1af79b81cdda8b1875712d2131", "analysis_result": "分析出错: <PERSON><PERSON><PERSON> communicating with LLM", "last_updated": "2025-05-27T09:53:30.716522"}}, "file_info": {"file_path": "d:\\bigModel\\curser\\stock_test1\\backend\\MetaGPT\\examples\\a_ymg_learn\\ex.py", "total_lines": 158, "file_size": 4057, "last_modified": "2025-03-17 17:43:08.923419"}, "call_graph": {}, "created_at": "2025-05-27T09:53:30.713521", "updated_at": "2025-05-27T09:53:30.717521"}