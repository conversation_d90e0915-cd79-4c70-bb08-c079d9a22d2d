[{"id": "functionality_requirements", "name": "文档是否包含明确的功能需求", "description": "检查文档是否包含清晰、具体的功能需求描述", "criteria": {"satisfied": "文档详细列出了所有功能需求，包括具体的实现方法和预期结果", "partially_satisfied": "文档提到了一些功能需求，但描述不够详细或不够具体", "not_satisfied": "文档完全没有提到功能需求或提到的信息过于模糊"}, "examples": {"satisfied": "需要开发用户登录功能，支持账号密码和短信验证码两种登录方式", "partially_satisfied": "系统需要支持用户登录功能", "not_satisfied": "文档中没有任何关于功能的描述"}}, {"id": "interface_definitions", "name": "是否定义了系统接口", "description": "检查文档是否定义了系统内部和外部的接口规范", "criteria": {"satisfied": "文档详细描述了所有接口的名称、参数、返回值和调用方式", "partially_satisfied": "文档提到了一些接口，但描述不够详细", "not_satisfied": "文档完全没有提到任何接口信息"}, "examples": {"satisfied": "用户登录API：POST /api/login，参数：username, password，返回：token", "partially_satisfied": "系统将提供用户登录的接口", "not_satisfied": "文档中没有任何关于接口的描述"}}, {"id": "deadline_specifications", "name": "是否包含具体的截止时间", "description": "检查文档是否包含明确、具体的项目或任务截止时间", "criteria": {"satisfied": "文档中明确指定了具体的截止日期（精确到年月日），能够清晰了解项目完成时间要求", "partially_satisfied": "文档中提到了时间要求，但没有具体日期或仅有模糊的时间范围", "not_satisfied": "文档中完全没有提到任何与截止时间相关的信息"}, "examples": {"satisfied": "系统开发需要在2025年4月10日前完成并交付使用", "partially_satisfied": "系统应在Q2季度完成，系统需在几个月内完成", "not_satisfied": "文档中没有任何关于时间要求或截止日期的描述"}}, {"id": "developer_information", "name": "是否包含开发人员信息", "description": "检查文档是否包含项目开发人员的详细信息，包括姓名、职责分工和联系方式等", "criteria": {"satisfied": "文档详细列出了所有开发人员的姓名、职责、角色以及联系方式", "partially_satisfied": "文档提到了开发团队或部分开发人员，但信息不完整或不够详细", "not_satisfied": "没有提到任何开发人员相关信息"}, "examples": {"satisfied": "项目开发团队：张三（项目经理，联系电话：12345678901），李四（前端开发，邮箱：<EMAIL>），王五（后端开发）", "partially_satisfied": "本项目由技术部门负责开发，联系人：张经理", "not_satisfied": "文档中没有任何关于开发人员或团队的信息"}}]