# 类: SimpleWriteReview

**文件路径**: d:\bigModel\curser\stock_test1\backend\MetaGPT\examples\a_ymg_learn\ex.py
**行号范围**: 109-122
**最后更新**: 2025-05-27 09:53:30
**继承**: Action

## 方法列表
### run
- **行号**: 117-122
- **参数**: self, context

## 方法调用关系
### SELF 调用
- SimpleWriteReview.run → SimpleWriteReview._aask (行 120)

## 源代码
```python
class SimpleWriteReview(Action):
    PROMPT_TEMPLATE: str = """
    Context: {context}
    Review the test cases and provide one critical comments:
    """

    name: str = "SimpleWriteReview"

    async def run(self, context: str):
        prompt = self.PROMPT_TEMPLATE.format(context=context)

        rsp = await self._aask(prompt)

        return rsp
```

## 智能分析
分析出错: Error communicating with LLM