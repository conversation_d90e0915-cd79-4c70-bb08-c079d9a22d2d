#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2024/3/11
<AUTHOR> ale<PERSON><PERSON><PERSON>
@File    : function_actions.py
"""
from datetime import datetime
from typing import Dict, List, ClassVar

from metagpt.actions import Action
from metagpt.logs import logger

class AnalyzeFunctionAction(Action):
    """分析代码函数"""
    name: str = "AnalyzeFunctionAction"
    
    ANALYSIS_PROMPT: ClassVar[str] = """
请分析以下代码:

```python
{code}
```

请提供以下分析：

1. 功能说明
    - 主要功能
    - 使用场景
    - 代码特点
    - 应用示例
    
2. 参数和返回值分析
    - 输入参数
        - 参数名称
        - 参数类型
        - 参数用途
        - 参数限制条件
    - 返回值
        - 返回类型
        - 返回值说明
        - 可能的返回情况
        
3. 实现逻辑分析
    - 主要步骤
    - 关键算法
    - 异常处理
    - 性能分析
    - 可能的优化点
    
4. 调用关系图
    
5. 代码质量分析
    - 代码可读性
    - 代码复杂度
    - 代码维护性
    - 改进建议
"""
    
    async def run(self, context: Dict) -> Dict:
        """分析函数"""
        try:
            source_code = context.get('source_code', '')
            start_line = context.get('start_line', 1)
            end_line = context.get('end_line', 1)
            function_name = context.get('function_name', 'unknown')
            file_path = context.get('file_path', 'unknown')
            
            if not source_code:
                return {
                    'error': '没有提供源代码',
                    'analyzed_at': datetime.now().isoformat()
                }
            
            # 提取函数代码
            code_lines = source_code.splitlines()
            function_code = '\n'.join(code_lines[start_line-1:end_line])
            
            # 使用LLM分析函数
            if hasattr(self.llm, "aask"):
                analysis = await self.llm.aask(
                    self.ANALYSIS_PROMPT.format(code=function_code)
                )
            else:
                analysis = "LLM未就绪，无法进行深入分析"
            
            return {
                'function_name': function_name,
                'file_path': file_path,
                'start_line': start_line,
                'end_line': end_line,
                'analysis': analysis,
                'metadata': {
                    'lines_of_code': end_line - start_line + 1,
                    'analyzed_at': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            error_msg = f"分析函数出错: {str(e)}"
            logger.error(error_msg)
            return {
                'error': error_msg,
                'function_name': context.get('function_name', 'unknown'),
                'file_path': context.get('file_path', 'unknown'),
                'analyzed_at': datetime.now().isoformat()
            } 