#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time    : 2024/3/11
<AUTHOR> ale<PERSON><PERSON><PERSON>
@File    : base_analyzer.py
"""
from datetime import datetime
from typing import Optional

from metagpt.roles import Role
from metagpt.schema import Message
from metagpt.logs import logger
from metagpt.actions import Action

from ..schema import AnalysisMessage, AnalysisState

class BaseAnalyzerAction(Action):
    """基础分析动作"""
    name: str = "BaseAnalyzerAction"

    async def run(self, context: str) -> str:
        """执行分析"""
        return "基础分析完成"

class BaseAnalyzer(Role):
    """基础分析器角色"""
    
    name: str = "BaseAnalyzer"
    profile: str = "BaseAnalyzer"
    goal: str = "分析代码"
    constraints: str = "遵循代码分析规范"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.state = AnalysisState()
        # 设置默认Action
        self.set_actions([BaseAnalyzerAction])
        logger.info(f"{self.profile} 初始化完成，设置了默认动作")
        
    async def _act(self) -> Message:
        """执行分析动作"""
        logger.info(f"{self.profile} 开始执行 {self.rc.todo}")
        
        try:
            # 确保有可执行的动作
            if not self.rc.todo:
                logger.warning(f"{self.profile} 没有可执行的动作")
                return AnalysisMessage(
                    content="没有可执行的动作",
                    role=self.profile,
                    phase="error",
                    processor={self.profile},
                    dependencies=set()
                )
            
            # 获取记忆
            memories = self.get_memories()
            logger.info(f"{self.profile} 获取到记忆: {memories[:200] if memories else 'None'}")
            
            # 执行具体的分析动作
            result = await self.rc.todo.run(memories)
            logger.info(f"{self.profile} 执行结果: {result[:200] if result else 'None'}")
            
            # 更新状态
            self.state.last_updated = datetime.now()
            logger.info(f"{self.profile} 状态已更新: {self.state}")
            
            # 返回分析消息
            return AnalysisMessage(
                content=result,
                role=self.profile,
                cause_by=type(self.rc.todo),
                phase=self.state.phase,
                processor={self.profile},
                dependencies=set()
            )
            
        except Exception as e:
            logger.error(f"{self.profile} 处理消息出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return AnalysisMessage(
                content=str(e),
                role=self.profile,
                cause_by=type(self.rc.todo),
                phase="error",
                processor={self.profile},
                dependencies=set()
            )
            
    def get_memories(self) -> str:
        """获取记忆中的消息"""
        # 默认返回最后一条消息的内容
        msgs = self.rc.memory.get_by_role(self.profile)
        result = msgs[-1].content if msgs else ""
        logger.info(f"{self.profile} 获取记忆: {result[:200] if result else 'None'}")
        return result

    def update_state(self, phase: str, result: dict = None, error: str = ""):
        """更新状态"""
        logger.info(f"{self.profile} 更新状态: phase={phase}, error={error}")
        self.state.phase = phase
        if error:
            self.state.error = error
        self.state.last_updated = datetime.now()
        
        # 更新环境中的状态
        if hasattr(self.rc.env, "update_state"):
            self.rc.env.update_state(**{"phase": phase, "error": error})
            logger.info(f"{self.profile} 环境状态已更新")
            
    def check_dependencies(self, dependencies: set) -> bool:
        """检查依赖是否满足"""
        if not dependencies:
            return True
            
        if hasattr(self.rc.env, "check_dependencies"):
            result = self.rc.env.check_dependencies(dependencies)
            logger.info(f"{self.profile} 依赖检查结果: {result}, 依赖项: {dependencies}")
            return result
        return False
        
    async def _process(self, msg: Message) -> Optional[Message]:
        """处理消息的具体逻辑，由子类实现"""
        raise NotImplementedError 