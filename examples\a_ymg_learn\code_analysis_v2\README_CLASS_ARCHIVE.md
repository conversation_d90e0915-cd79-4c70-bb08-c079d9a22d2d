# 代码分析助手 V2.0 - 类归档增强版

这是一个基于MetaGPT框架的高级代码分析工具，专门优化了**按类归档存储AST信息**的功能，能够深度分析Python代码的结构、方法调用关系，并提供大模型的自然语言解释。

## 🚀 核心优化功能

### 1. **按类归档存储**
- 为每个类创建独立的AST信息存储
- 包含完整的源代码、AST dump、哈希值等
- 支持增量更新和变更检测

### 2. **方法调用关系分析**
- 分析类内部方法调用（self调用）
- 分析类间方法调用（external调用）
- 分析继承关系调用（super调用）
- 分析静态函数调用（static调用）

### 3. **大模型自然语言解释**
- 为每个类生成详细的功能说明
- 包含代码质量评估和改进建议
- 提供使用示例和最佳实践

### 4. **结构化文档生成**
- 每个类生成独立的Markdown文档
- 包含源代码、AST信息、调用关系图
- 支持Mermaid图表可视化

## 📁 输出结构

```
project/
├── class_archives/           # JSON格式的类归档文件
│   ├── archive_filename_20241209_143022.json
│   └── ...
├── class_docs_filename/      # 按文件名分组的类文档
│   ├── README.md            # 总览文档
│   ├── ClassName1.md        # 类1的详细文档
│   ├── ClassName2.md        # 类2的详细文档
│   └── call_graph.md        # 调用关系图
└── code_analysis_report.md  # 传统分析报告
```

## 🛠️ 使用方法

### 1. 完整分析（推荐）

```bash
# 分析单个文件，启用所有功能
python code_analyzer_v2.py your_file.py --enable_class_archive=True --enable_llm_analysis=True

# 分析整个目录
python code_analyzer_v2.py /path/to/project --enable_class_archive=True --enable_llm_analysis=True
```

### 2. 快速分析（仅AST，不使用LLM）

```bash
# 快速分析，不使用LLM
python code_analyzer_v2.py your_file.py --enable_class_archive=True --enable_llm_analysis=False
```

### 3. 编程接口

```python
import asyncio
from code_analyzer_v2 import analyze_single_file, ClassArchiveManager

async def main():
    # 分析单个文件
    archive = await analyze_single_file("your_file.py", enable_llm=True)
    
    # 查看分析结果
    for class_name, class_info in archive.classes.items():
        print(f"类: {class_name}")
        print(f"方法数: {len(class_info.methods)}")
        print(f"调用关系: {len(class_info.method_calls)}")
    
    # 管理归档
    manager = ClassArchiveManager()
    manager.save_archive(archive, "my_archive.json")
    manager.save_class_docs(archive, "my_docs")

asyncio.run(main())
```

## 📊 类归档数据结构

每个类的归档包含以下信息：

```python
@dataclass
class ClassASTInfo:
    name: str                    # 类名
    file_path: str              # 文件路径
    start_line: int             # 起始行号
    end_line: int               # 结束行号
    bases: List[str]            # 继承的基类
    decorators: List[str]       # 装饰器
    docstring: str              # 文档字符串
    methods: Dict[str, Dict]    # 方法信息
    attributes: List[str]       # 类属性
    method_calls: List[MethodCallInfo]  # 方法调用关系
    ast_dump: str               # AST的字符串表示
    source_code: str            # 源代码
    hash_value: str             # 代码哈希值
    analysis_result: str        # LLM分析结果
    last_updated: datetime      # 最后更新时间
```

## 🔍 方法调用关系分析

系统能够识别以下类型的方法调用：

1. **SELF调用**: `self.method()` - 类内部方法调用
2. **SUPER调用**: `super().method()` - 父类方法调用
3. **EXTERNAL调用**: `obj.method()` - 外部对象方法调用
4. **STATIC调用**: `function()` - 静态函数调用

每个调用包含：
- 调用者方法名
- 被调用方法名
- 调用行号
- 调用类型
- 目标类名（如果适用）

## 🤖 LLM分析内容

对每个类，LLM会提供：

1. **类功能概述**
   - 主要职责和功能
   - 设计模式和架构角色
   - 使用场景和应用领域

2. **类结构分析**
   - 继承关系分析
   - 属性和方法的组织方式
   - 接口设计和封装性

3. **方法调用流程**
   - 内部方法调用关系
   - 外部依赖分析
   - 数据流向分析

4. **代码质量评估**
   - 代码复杂度分析
   - 可维护性评估
   - 潜在问题和改进建议

5. **使用示例**
   - 典型的使用方式
   - 关键方法的调用顺序
   - 注意事项和最佳实践

## 📈 优势对比

| 功能 | 原版本 | 类归档增强版 |
|------|--------|-------------|
| 类信息存储 | 基础信息 | 完整AST+源码+哈希 |
| 方法调用分析 | 无 | 详细的调用关系图 |
| 文档生成 | 统一报告 | 按类独立文档 |
| 增量更新 | 不支持 | 基于哈希值检测变更 |
| LLM分析 | 函数级别 | 类级别深度分析 |
| 数据持久化 | 临时 | JSON格式持久化 |
| 可视化 | 基础图表 | Mermaid调用关系图 |

## 🎯 使用场景

1. **代码重构**: 了解类之间的依赖关系，安全重构
2. **代码审查**: 快速理解代码结构和设计模式
3. **文档生成**: 自动生成详细的技术文档
4. **架构分析**: 分析系统的类层次结构
5. **代码学习**: 通过LLM解释快速理解复杂代码

## 🚀 快速开始

1. 运行演示：
```bash
python class_archive_demo.py
```

2. 分析你的代码：
```bash
python code_analyzer_v2.py your_project/ --enable_class_archive=True
```

3. 查看生成的文档：
- 打开 `class_docs_*/README.md` 查看总览
- 查看各个类的独立文档
- 查看 `call_graph.md` 了解调用关系

## 📝 注意事项

- LLM分析需要网络连接和API配置
- 大型项目建议分批分析
- 生成的文档支持Markdown预览
- JSON归档文件可用于后续分析和比较

## 🔧 依赖要求

- Python 3.8+
- MetaGPT框架
- 相关依赖包（详见requirements.txt）
