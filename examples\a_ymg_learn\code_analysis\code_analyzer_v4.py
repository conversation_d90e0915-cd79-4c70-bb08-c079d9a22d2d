#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码分析助手 V4.0
作者: Assistant
创建时间: 2024-03-11
更新时间: 2024-03-14

改进:
1. 使用专门的消息类型
2. 添加状态管理
3. 实现主动依赖检查
4. 使用环境对象控制消息流转
"""

import os
import json
import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Optional

from metagpt.actions import Action, UserRequirement
from metagpt.logs import logger
from metagpt.roles import Role
from metagpt.team import Team
from metagpt.context import Context

from .environment import CodeAnalysisEnvironment
from .schema import (
    AnalysisMessage,
    AnalysisState,
    FileInfo,
    CodeStructure,
    AnalysisReport
)

class CodeAnalysisTeam(Team):
    """代码分析团队，使用改进的消息和状态管理机制"""
    
    def __init__(self, context: Context = None, **kwargs):
        super().__init__(**kwargs)
        ctx = context or Context()
        self.env = CodeAnalysisEnvironment(context=ctx)
            
    def run_analysis(self, config: dict):
        """启动分析任务"""
        # 将配置转换为JSON字符串
        config_str = json.dumps(config, ensure_ascii=False)
        logger.info(f"启动分析任务，配置: {config_str}")
        
        self.env.publish_message(
            AnalysisMessage(
                role="Controller",
                content=config_str,
                phase="init",
                processor={"FileAnalyzer"},
                dependencies=set()
            )
        )
        logger.info("初始消息已发布")

async def main(
    target_path: str = ".",
    investment: float = 300.0,
    output_file: str = "code_analysis_report.md",
    max_rounds: int = 5,
    idea: str = None,
):
    """主函数"""
    # 确保使用绝对路径
    abs_target_path = str(Path(target_path).absolute())
    logger.info(f"开始分析目标: {abs_target_path}")
    
    # 验证目标路径
    if not Path(abs_target_path).exists():
        logger.error(f"目标路径不存在: {abs_target_path}")
        return
        
    # 创建分析团队
    team = CodeAnalysisTeam()
    logger.info("分析团队已创建")
    
    # 创建并添加团队成员
    from .roles import FileAnalyzer, StructureAnalyzer, FunctionAnalyzer, ReportGenerator
    roles = [
        FileAnalyzer(),
        StructureAnalyzer(),
        FunctionAnalyzer(),
        ReportGenerator()
    ]
    team.hire(roles)
    logger.info(f"已雇佣团队成员: {[role.profile for role in roles]}")
    
    # 投资
    team.invest(investment)
    logger.info(f"投资金额: {investment}")
    
    # 创建配置
    config = {
        "task": "code_analysis",
        "target_path": abs_target_path,
        "output_file": output_file,
        "description": idea if idea else "分析代码结构和功能"
    }
    
    # 启动分析
    team.run_analysis(config)
    
    # 运行团队
    logger.info("开始团队运行...")
    try:
        # 修改为逐轮执行
        for round_num in range(max_rounds):
            logger.info(f"=== 开始第 {round_num + 1} 轮执行 ===")
            
            # 获取当前环境中的消息
            current_messages = team.env.analysis_messages
            logger.info(f"当前轮次开始时的消息数量: {len(current_messages)}")
            
            # 执行一轮
            await team.run(n_round=1)
            await asyncio.sleep(1)  # 添加短暂延迟确保消息处理
            
            # 检查新消息
            new_messages = team.env.analysis_messages
            logger.info(f"当前轮次结束时的消息数量: {len(new_messages)}")
            
            # 如果没有新消息产生，提前结束
            if len(new_messages) == len(current_messages):
                logger.warning(f"第 {round_num + 1} 轮没有产生新消息，提前结束")
                break
                
            # 输出最新消息的详细信息
            latest_msg = new_messages[-1] if new_messages else None
            if latest_msg:
                logger.info(f"最新消息: phase={latest_msg.phase}, role={latest_msg.role}, "
                          f"processor={latest_msg.processor}, content={latest_msg.content[:100]}")
            
            # 检查是否已经生成报告
            if latest_msg and latest_msg.phase == "report":
                logger.info("报告已生成，结束执行")
                break
                
        logger.info(f"团队运行完成，实际执行了 {round_num + 1} 轮")
        
        # 检查环境中的消息
        all_messages = team.env.analysis_messages
        logger.info(f"环境中的消息总数: {len(all_messages)}")
        logger.info("所有消息概况:")
        for i, msg in enumerate(all_messages):
            logger.info(f"消息 {i+1}: phase={msg.phase}, role={msg.role}, "
                      f"processor={msg.processor}, content长度={len(msg.content)}")
        
    except Exception as e:
        logger.error(f"团队运行出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    
    try:
        # 获取最终报告
        report_msg = team.env.get_latest_message(phase="report")
        logger.info(f"获取到的报告消息: {report_msg}")
        
        if report_msg and report_msg.content:
            # 写入报告
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(report_msg.content)
            logger.info(f"分析完成，结果已保存到: {output_file}")
        else:
            logger.error("未能生成分析报告")
            # 检查每个角色的状态和记忆
            for role in roles:
                logger.info(f"角色 {role.profile} 的状态: phase={role.state.phase}, error={role.state.error}")
                memories = role.get_memories()
                logger.info(f"角色 {role.profile} 的记忆: {memories[:200] if memories else 'None'}")
    except Exception as e:
        logger.error(f"生成报告出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    import fire
    fire.Fire(main) 