# 类: SimpleWriteTest

**文件路径**: d:\bigModel\curser\stock_test1\backend\MetaGPT\examples\a_ymg_learn\ex.py
**行号范围**: 66-83
**最后更新**: 2025-05-27 09:53:30
**继承**: Action

## 方法列表
### run
- **行号**: 76-83
- **参数**: self, context, k

## 方法调用关系
### STATIC 调用
- SimpleWriteTest.run → parse_code (行 81)
### SELF 调用
- SimpleWriteTest.run → SimpleWriteTest._aask (行 79)

## 源代码
```python
class SimpleWriteTest(Action):
    PROMPT_TEMPLATE: str = """
    Context: {context}
    Write {k} unit tests using pytest for the given function, assuming you have imported it.
    Return ```python your_code_here ``` with NO other texts,
    your code:
    """

    name: str = "SimpleWriteTest"

    async def run(self, context: str, k: int = 3):
        prompt = self.PROMPT_TEMPLATE.format(context=context, k=k)

        rsp = await self._aask(prompt)

        code_text = parse_code(rsp)

        return code_text
```

## 智能分析
分析出错: Error communicating with LLM