#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码分析环境
作者: Assistant
创建时间: 2024-03-11
更新时间: 2024-03-14
"""

from typing import Optional, List, Dict
from datetime import datetime
from pydantic import Field
from metagpt.environment import Environment
from metagpt.schema import Message
from metagpt.context import Context
from metagpt.memory.memory import Memory

from .schema import AnalysisMessage, AnalysisState

class CodeAnalysisEnvironment(Environment):
    """代码分析环境，管理消息流转和状态"""
    
    analysis_messages: List[AnalysisMessage] = Field(default_factory=list)
    analysis_state: AnalysisState = Field(default_factory=AnalysisState)
    memory_storage: Memory = Field(default_factory=Memory)
    
    def __init__(self, context: Context = None):
        super().__init__(context=context)
        
    def publish_message(self, message: AnalysisMessage):
        """发布消息到环境"""
        self.analysis_messages.append(message)
        self.memory_storage.add(message)
        
    def get_latest_message(self, phase: str = None) -> Optional[AnalysisMessage]:
        """获取最新的消息"""
        if not phase:
            return self.analysis_messages[-1] if self.analysis_messages else None
            
        for msg in reversed(self.analysis_messages):
            if msg.phase == phase:
                return msg
        return None
        
    def get_messages_by_phase(self, phase: str) -> List[AnalysisMessage]:
        """获取指定阶段的所有消息"""
        return [msg for msg in self.analysis_messages if msg.phase == phase]
        
    def get_state(self) -> AnalysisState:
        """获取当前状态"""
        return self.analysis_state
        
    def update_state(self, **kwargs):
        """更新状态"""
        for key, value in kwargs.items():
            if hasattr(self.analysis_state, key):
                setattr(self.analysis_state, key, value)
                
    def clear(self):
        """清理环境"""
        self.analysis_messages.clear()
        self.analysis_state = AnalysisState()
        self.memory_storage.clear() 