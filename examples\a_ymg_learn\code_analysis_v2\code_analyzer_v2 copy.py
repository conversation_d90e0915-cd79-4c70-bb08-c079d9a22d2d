"""
代码分析助手 V2.0
作者: yuanminggui
创建时间: 2024-03-09
"""

import os
import re
import ast
import sys
import json
import time
from datetime import datetime
from typing import List, Dict, Optional, Any, Union, Callable, ClassVar
from pathlib import Path
from dataclasses import dataclass
from collections import defaultdict
from metagpt.actions import Action, UserRequirement
from metagpt.logs import logger
from metagpt.roles import Role
from metagpt.schema import Message
from metagpt.team import Team


# ======= 消息类型 =======

class AgentMessage:
    """代理消息基类"""

    def __init__(self, content: Any, msg_type: str, sender: str, receiver: str = ""):
        self.content = content
        self.msg_type = msg_type
        self.sender = sender
        self.receiver = receiver
        self.timestamp = time.time()

    def __str__(self):
        return f"[{self.msg_type}] From {self.sender} to {self.receiver}: {self.content}"


@dataclass
class FileAnalysisMessage(AgentMessage):
    """文件分析消息"""

    def __init__(self, files: List[str], total_files: int, is_file: bool, is_dir: bool,
                 sender: str, receiver: str = "", error: Optional[str] = None):
        super().__init__(
            content={
                'files': files,
                'total_files': total_files,
                'is_file': is_file,
                'is_dir': is_dir,
                'error': error
            },
            msg_type="FILE_ANALYSIS",
            sender=sender,
            receiver=receiver
        )


@dataclass
class CodeStructureMessage(AgentMessage):
    """代码结构分析消息"""

    def __init__(self, file_path: str, classes: List[Dict], functions: List[Dict],
                 imports: List[str], standalone_code: List[Dict], sender: str,
                 receiver: str = "", error: Optional[str] = None):
        super().__init__(
            content={
                'file_path': file_path,
                'classes': classes,
                'functions': functions,
                'imports': imports,
                'standalone_code': standalone_code,
                'error': error
            },
            msg_type="CODE_STRUCTURE",
            sender=sender,
            receiver=receiver
        )


@dataclass
class CodeFunctionMessage(AgentMessage):
    """代码函数分析消息"""

    def __init__(self, function_name: str, analysis_result: str,
                 sender: str, receiver: str = "", error: Optional[str] = None):
        super().__init__(
            content={
                'function_name': function_name,
                'analysis_result': analysis_result,
                'error': error
            },
            msg_type="CODE_FUNCTION",
            sender=sender,
            receiver=receiver
        )


# ======= 消息总线 =======
class MessageBus:
    """消息总线"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MessageBus, cls).__new__(cls)
            cls._instance.subscribers = defaultdict(list)
            cls._instance.message_history = []
        return cls._instance

    def subscribe(self, msg_type: str, callback: Callable):
        """订阅消息"""
        self.subscribers[msg_type].append(callback)
        logger.info(f"已订阅消息类型: {msg_type}")

    def publish(self, message: AgentMessage):
        """发布消息"""
        self.message_history.append(message)
        logger.info(str(message))

        for callback in self.subscribers[message.msg_type]:
            callback(message)

    def get_history(self, msg_type: Optional[str] = None) -> List[AgentMessage]:
        """获取消息历史"""
        if msg_type:
            return [msg for msg in self.message_history if msg.msg_type == msg_type]
        return self.message_history

    def to_dict(self) -> Dict:
        """转换为字典，用于序列化"""
        return {
            'message_history': [
                {
                    'content': msg.content,
                    'msg_type': msg.msg_type,
                    'sender': msg.sender,
                    'receiver': msg.receiver,
                    'timestamp': msg.timestamp
                }
                for msg in self.message_history
            ]
        }


# ======= 工具类 =======
class MarkdownFormatter:
    """Markdown格式化工具"""

    @staticmethod
    def format_file_analysis(msg: AgentMessage) -> str:
        """格式化文件分析结果"""
        content = msg.content
        result = ["## 文件分析结果\n"]

        if content['error']:
            result.append(f"### 错误信息\n{content['error']}\n")
            return "\n".join(result)

        result.extend([
            f"- 类型: {'文件' if content['is_file'] else '目录'}",
            f"- 总文件数: {content['total_files']}",
            "\n### 文件列表"
        ])

        for file in content['files']:
            result.append(f"- {file}")

        return "\n".join(result)

    @staticmethod
    def format_code_structure(msg: AgentMessage) -> str:
        """格式化代码结构分析结果"""
        content = msg.content
        result = [f"## 代码结构分析: {os.path.basename(content['file_path'])}\n"]

        if content['error']:
            result.append(f"### 错误信息\n{content['error']}\n")
            return "\n".join(result)

        # 生成导入关系图
        result.append("### 导入依赖\n```mermaid\ngraph LR")
        current_file = os.path.basename(content['file_path']).replace(".py", "")
        for imp in content['imports']:
            result.append(f"    {current_file}-->{imp}")
        result.append("```\n")

        # 生成类图
        if content['classes']:
            result.append("### 类结构\n```mermaid\nclassDiagram")
            for cls in content['classes']:
                result.append(f"    class {cls['name']} {{")
                for method in cls['methods']:
                    result.append(f"        +{method['name']}()")
                result.append("    }")
            result.append("```\n")

        # 生成函数列表
        if content['functions']:
            result.append("### 独立函数")
            for func in content['functions']:
                result.append(
                    f"- {func['name']} (行 {func['start_line']}-{func['end_line']})")
            result.append("")

        # 添加独立代码块的展示
        if content['standalone_code']:
            result.append("### 独立代码块")
            for code_block in content['standalone_code']:
                result.append(
                    f"#### {code_block['type']} (行 {code_block['start_line']}-{code_block['end_line']})")
                result.append("```python")
                result.append(code_block['code'])
                result.append("```\n")
        return "\n".join(result)

    @staticmethod
    def format_code_function(msg: AgentMessage) -> str:
        """格式化代码函数分析结果"""
        content = msg.content

        # 解析函数名中的信息
        name = content['function_name']
        if name.startswith('独立代码块'):
            # 对于独立代码块，标题格式为：代码分析: 独立代码块 (类型, 行 X-Y)
            result = [f"## 代码分析: {name}\n"]
        elif '.' in name:
            # 对于类方法，标题格式为：代码分析: 类方法 (类名.方法名, 行 X-Y)
            result = [f"## 代码分析: 类方法 ({name})\n"]
        else:
            # 对于普通函数，标题格式为：代码分析: 函数 (函数名, 行 X-Y)
            result = [f"## 代码分析: 函数 ({name})\n"]

        if content['error']:
            result.append(f"### 错误信息\n{content['error']}\n")
            return "\n".join(result)
        # 添加分隔线，使报告更清晰
        result.append("---\n")
        result.append(content['analysis_result'])
        return "\n".join(result)

    @staticmethod
    def generate_report(messages: List[AgentMessage]) -> str:
        """生成完整分析报告"""
        content = [
            "# 代码分析报告",
            f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        ]

        # 按消息类型分组
        for msg in messages:
            if msg.msg_type == "FILE_ANALYSIS":
                content.append(MarkdownFormatter.format_file_analysis(msg))
            elif msg.msg_type == "CODE_STRUCTURE":
                content.append("\n" + MarkdownFormatter.format_code_structure(msg))
            elif msg.msg_type == "CODE_FUNCTION":
                content.append("\n" + MarkdownFormatter.format_code_function(msg))

        return "\n".join(content)


# ======= 不调用LLM的Actions =======

class ParseFilePath(Action):
    """解析文件路径的动作类"""
    name: str = "ParseFilePath"

    async def run(self, target_path: str) -> FileAnalysisMessage:
        """
        解析目标路径，返回文件信息
        
        Args:
            target_path: 目标路径
            
        Returns:
            FileAnalysisMessage: 文件分析消息
        """
        try:
            path = Path(target_path)
            if path.is_file():
                return FileAnalysisMessage(
                    files=[str(path.absolute())],
                    total_files=1,
                    is_file=True,
                    is_dir=False,
                    sender=self.name,
                    receiver=self.name
                )
            elif path.is_dir():
                files = [str(f.absolute()) for f in path.rglob('*.py')]
                return FileAnalysisMessage(
                    files=files,
                    total_files=len(files),
                    is_file=False,
                    is_dir=True,
                    sender=self.name,
                    receiver=self.name
                )
            else:
                return FileAnalysisMessage(
                    files=[],
                    total_files=0,
                    is_file=False,
                    is_dir=False,
                    error=f"路径 {target_path} 不存在",
                    sender=self.name,
                    receiver=self.name
                )

        except Exception as e:
            return FileAnalysisMessage(
                files=[],
                total_files=0,
                is_file=False,
                is_dir=False,
                error=str(e),
                sender=self.name,
                receiver=self.name
            )


class AnalyzeCodeStructure(Action):
    """分析代码结构的动作类"""
    name: str = "AnalyzeCodeStructure"

    async def run(self, file_path: str) -> CodeStructureMessage:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()

            tree = ast.parse(code)
            lines = code.splitlines()

            # 初始化结果容器
            classes = []
            functions = []
            imports = []
            standalone_code = []  # 新增：用于存储独立代码块

            # 解析导入语句
            self._parse_imports(tree, imports)

            # 解析所有顶层节点
            for node in tree.body:
                self._process_node(node, lines, classes, functions, standalone_code)

            return CodeStructureMessage(
                file_path=file_path,
                classes=classes,
                functions=functions,
                imports=imports,
                standalone_code=standalone_code,  # 新增：返回独立代码块
                sender=self.name,
                receiver=self.name
            )

        except Exception as e:
            logger.error(f"分析文件 {file_path} 结构出错: {str(e)}")
            return CodeStructureMessage(
                file_path=file_path,
                classes=[],
                functions=[],
                imports=[],
                standalone_code=[],  # 新增：空的独立代码块
                error=str(e),
                sender=self.name,
                receiver=self.name
            )

    def _parse_imports(self, tree: ast.AST, imports: List[str]):
        """解析导入语句
        
        Args:
            tree: AST树
            imports: 导入列表，用于存储结果
        """
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for name in node.names:
                    imports.append(name.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                for name in node.names:
                    imports.append(f"{module}.{name.name}")

    def _process_node(self, node: ast.AST, lines: List[str],
                      classes: List[Dict], functions: List[Dict],
                      standalone_code: List[Dict],
                      parent_class: str = None):
        """递归处理AST节点"""
        if isinstance(node, ast.ClassDef):
            # 处理类定义
            class_info = self._parse_class(node, lines, parent_class)
            classes.append(class_info)

            # 递归处理类的内部节点
            for child in node.body:
                self._process_node(child, lines, classes, functions, standalone_code,
                                   node.name)

        elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            # 处理函数定义
            func_info = self._parse_function(node, lines, parent_class)
            functions.append(func_info)

            # 递归处理函数内部的类和函数定义
            for child in node.body:
                if isinstance(child,
                              (ast.ClassDef, ast.FunctionDef, ast.AsyncFunctionDef)):
                    self._process_node(child, lines, classes, functions, standalone_code,
                                       parent_class)

        elif self._is_main_check(node):
            # 处理 if __name__ == '__main__' 代码块
            main_code = []
            for child in node.body:
                if isinstance(child,
                              (ast.ClassDef, ast.FunctionDef, ast.AsyncFunctionDef)):
                    self._process_node(child, lines, classes, functions, standalone_code)
                else:
                    # 记录非函数定义的代码行
                    main_code.append(lines[child.lineno - 1].strip())

            if main_code:
                functions.append({
                    'name': '__main__',
                    'start_line': node.lineno,
                    'end_line': node.end_lineno,
                    'is_main_block': True,
                    'main_code': main_code
                })

        elif isinstance(node, ast.Try):
            # 处理try-except结构
            try_block = {
                'type': 'try-except',
                'start_line': node.lineno,
                'end_line': node.end_lineno,
                'body': [],
                'handlers': [],
                'else_body': [],
                'finally_body': []
            }

            # 处理try块
            for child in node.body:
                if isinstance(child, (ast.Import, ast.ImportFrom)):
                    self._parse_imports(child, imports)
                else:
                    try_block['body'].append(lines[child.lineno - 1].strip())

            # 处理except块
            for handler in node.handlers:
                handler_info = {
                    'type': handler.type.id if handler.type else 'BaseException',
                    'name': handler.name if handler.name else None,
                    'body': []
                }
                for child in handler.body:
                    if isinstance(child, (ast.Import, ast.ImportFrom)):
                        self._parse_imports(child, imports)
                    else:
                        handler_info['body'].append(lines[child.lineno - 1].strip())
                try_block['handlers'].append(handler_info)

            # 处理else块
            for child in node.orelse:
                if isinstance(child, (ast.Import, ast.ImportFrom)):
                    self._parse_imports(child, imports)
                else:
                    try_block['else_body'].append(lines[child.lineno - 1].strip())

            # 处理finally块
            for child in node.finalbody:
                if isinstance(child, (ast.Import, ast.ImportFrom)):
                    self._parse_imports(child, imports)
                else:
                    try_block['finally_body'].append(lines[child.lineno - 1].strip())

            standalone_code.append(try_block)

        else:
            # 处理其他独立语句
            if not isinstance(node, (ast.Import, ast.ImportFrom)):
                code_block = {
                    'type': node.__class__.__name__,
                    'start_line': node.lineno,
                    'end_line': getattr(node, 'end_lineno', node.lineno),
                    'code': lines[node.lineno - 1].strip()
                }
                standalone_code.append(code_block)

    def _parse_class(self, node: ast.ClassDef, lines: List[str],
                     parent_class: str = None) -> Dict:
        """解析类定义
        
        Args:
            node: 类定义节点
            lines: 源代码行
            parent_class: 父类名称
            
        Returns:
            Dict: 类信息字典
        """
        class_name = f"{parent_class}.{node.name}" if parent_class else node.name

        return {
            'name': class_name,
            'start_line': node.lineno,
            'end_line': self._get_end_line(node, lines),
            'bases': [base.id for base in node.bases if isinstance(base, ast.Name)],
            'decorators': [self._get_decorator_name(dec) for dec in node.decorator_list],
            'docstring': self._get_docstring(node),
            'methods': [],  # 方法将通过_process_node添加到functions中
            'is_nested': bool(parent_class)
        }

    def _parse_function(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef],
                        lines: List[str], parent_class: str = None) -> Dict:
        """解析函数定义
        
        Args:
            node: 函数定义节点
            lines: 源代码行
            parent_class: 父类名称（如果是方法）
            
        Returns:
            Dict: 函数信息字典
        """
        func_name = f"{parent_class}.{node.name}" if parent_class else node.name

        # 解析参数
        args_info = self._parse_arguments(node.args)

        return {
            'name': func_name,
            'start_line': node.lineno,
            'end_line': self._get_end_line(node, lines),
            'is_async': isinstance(node, ast.AsyncFunctionDef),
            'is_method': bool(parent_class),
            'args': args_info,
            'decorators': [self._get_decorator_name(dec) for dec in node.decorator_list],
            'docstring': self._get_docstring(node),
            'returns': self._get_return_annotation(node)
        }

    def _parse_arguments(self, args: ast.arguments) -> Dict:
        """解析函数参数
        
        Args:
            args: 参数节点
            
        Returns:
            Dict: 参数信息字典
        """
        return {
            'args': [arg.arg for arg in args.args],
            'defaults': len(args.defaults),
            'kwonly_args': [arg.arg for arg in args.kwonlyargs],
            'has_varargs': bool(args.vararg),
            'has_kwargs': bool(args.kwarg),
            'varargs_name': args.vararg.arg if args.vararg else None,
            'kwargs_name': args.kwarg.arg if args.kwarg else None
        }

    def _get_end_line(self, node: ast.AST, lines: List[str]) -> int:
        """获取节点的结束行号"""
        end_line = node.lineno
        for child in ast.iter_child_nodes(node):
            if hasattr(child, 'lineno'):
                child_end = getattr(child, 'end_lineno', child.lineno)
                end_line = max(end_line, child_end)
        return end_line

    def _get_docstring(self, node: ast.AST) -> Optional[str]:
        """获取节点的文档字符串"""
        docstring = ast.get_docstring(node)
        return docstring.strip() if docstring else None

    def _get_decorator_name(self, decorator: ast.expr) -> str:
        """获取装饰器名称"""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Call):
            if isinstance(decorator.func, ast.Name):
                return decorator.func.id
            elif isinstance(decorator.func, ast.Attribute):
                return f"{decorator.func.value.id}.{decorator.func.attr}"
        return "unknown_decorator"

    def _get_return_annotation(self, node: ast.FunctionDef) -> Optional[str]:
        """获取返回值注解"""
        if node.returns:
            if isinstance(node.returns, ast.Name):
                return node.returns.id
            elif isinstance(node.returns, ast.Constant):
                return str(node.returns.value)
            elif isinstance(node.returns, ast.Subscript):
                # 处理类似 List[str] 的情况
                return f"{node.returns.value.id}[{node.returns.slice.value.id}]"
        return None

    def _is_main_check(self, node: ast.AST) -> bool:
        """检查是否是 if __name__ == '__main__' 语句"""
        if not isinstance(node, ast.If):
            return False
        try:
            test = node.test
            return (isinstance(test, ast.Compare) and
                    isinstance(test.left, ast.Name) and test.left.id == '__name__' and
                    isinstance(test.ops[0], ast.Eq) and
                    isinstance(test.comparators[0], ast.Str) and test.comparators[
                        0].s == '__main__')
        except (AttributeError, IndexError):
            return False


# ======= 调用LLM的Actions =======

class PlanTask(Action):
    """任务规划动作类"""
    name: str = "PlanTask"
    PROMPT_TEMPLATE: ClassVar[str] = """
    请根据以下用户需求拆分任务：
    
    {requirement}
    
    请将任务拆分为具体的子任务，并按照以下格式返回：
    1. 子任务1
        - 具体步骤1
        - 具体步骤2
    2. 子任务2
        - 具体步骤1
        - 具体步骤2
    ...
    """

    async def run(self, requirement: str) -> str:
        """
        规划任务
        
        Args:
            requirement: 用户需求
            
        Returns:
            str: 任务计划
        """
        prompt = self.PROMPT_TEMPLATE.format(requirement=requirement)
        return await self._aask(prompt)


class AnalyzeCodeFunction(Action):
    """分析代码函数的动作类"""
    name: str = "AnalyzeCodeFunction"

    PROMPT_TEMPLATE: ClassVar[str] = """
    请分析以下代码:
    
    ```python
    {code}
    ```
    
    请提供以下分析：
    
    1. 功能说明
        - 主要功能
        - 使用场景
        - 代码类型（函数/类方法/独立代码块）
        
    2. 代码分析
        - 输入数据（变量、参数等）
        - 输出数据（返回值、修改的变量等）
        - 依赖项（导入的模块、使用的外部资源等）
            
    3. 实现逻辑分析
        - 主要步骤
        - 关键算法或操作
        - 数据处理流程
        - 异常处理（如果有）
    
    4. 代码质量分析
       - 代码可读性
       - 代码复杂度
       - 代码维护性
       - 改进建议

    5. 执行影响
       - 对系统资源的影响
       - 对数据的影响
       - 潜在的副作用
    """

    async def run(self, file_path: str, start_line: int, end_line: int,
                  block_type: str = "function",
                  block_name: str = "") -> CodeFunctionMessage:
        """
        分析代码块
        
        Args:
            file_path: 文件路径
            start_line: 起始行
            end_line: 结束行
            block_type: 代码块类型（function/method/standalone）
            block_name: 代码块名称
            
        Returns:
            CodeFunctionMessage: 代码分析消息
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                code = ''.join(lines[start_line - 1:end_line])

            # 根据代码块类型生成显示名称
            display_name = ""
            if block_type == "standalone":
                # 对于独立代码块，显示类型和行号范围
                block_type_name = block_name.split('(')[1].split(')')[0]  # 提取类型名称
                display_name = f"独立代码块 ({block_type_name}, 行 {start_line}-{end_line})"
            elif block_type == "method":
                # 对于类方法，显示完整方法名和行号范围
                display_name = f"{block_name} (行 {start_line}-{end_line})"
            else:
                # 对于普通函数，显示函数名和行号范围
                display_name = f"{block_name} (行 {start_line}-{end_line})"

            prompt = self.PROMPT_TEMPLATE.format(code=code)
            analysis_result = await self._aask(prompt)

            return CodeFunctionMessage(
                function_name=display_name,
                analysis_result=analysis_result,
                sender=self.name,
                receiver=self.name
            )

        except Exception as e:
            return CodeFunctionMessage(
                function_name=f"代码块 (行 {start_line}-{end_line})",
                analysis_result="",
                error=str(e),
                sender=self.name,
                receiver=self.name
            )


# ======= Roles =======

class BaseAnalyzerRole(Role):
    """基础分析者角色"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._message_bus = MessageBus()

    def subscribe_messages(self):
        """订阅消息，子类需要重写此方法"""
        pass

    def publish_message(self, message: Union[Message, AgentMessage]):
        """发布消息"""
        if isinstance(message, Message):
            # 将普通Message转换为AgentMessage
            agent_msg = AgentMessage(
                content=message.content,
                msg_type=self.__class__.__name__,  # 使用角色类名作为消息类型
                sender=self.name,
                receiver=""
            )
            self._message_bus.publish(agent_msg)
        elif isinstance(message, AgentMessage):
            self._message_bus.publish(message)

    def to_dict(self) -> Dict:
        """转换为字典，用于序列化"""
        base_dict = super().to_dict()
        base_dict['message_bus'] = self._message_bus.to_dict()
        return base_dict


class TaskPlanner(BaseAnalyzerRole):
    """任务规划者角色"""
    name: str = "TaskPlanner"
    profile: str = "任务规划专家"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.set_actions([PlanTask])
        self._watch([UserRequirement])
        self.subscribe_messages()

    def subscribe_messages(self):
        """订阅消息"""
        self._message_bus.subscribe("FILE_ANALYSIS", self.handle_file_analysis)
        self._message_bus.subscribe("CODE_STRUCTURE", self.handle_code_structure)
        self._message_bus.subscribe("CODE_FUNCTION", self.handle_code_function)

    def handle_file_analysis(self, message: AgentMessage):
        """处理文件分析消息"""
        logger.info(f"收到文件分析结果: {message}")
        # 可以在这里处理文件分析结果

    def handle_code_structure(self, message: AgentMessage):
        """处理代码结构分析消息"""
        logger.info(f"收到代码结构分析结果: {message}")
        # 可以在这里处理代码结构分析结果

    def handle_code_function(self, message: AgentMessage):
        """处理代码函数分析消息"""
        logger.info(f"收到代码函数分析结果: {message}")
        # 可以在这里处理代码函数分析结果

    async def _act(self) -> Message:
        logger.info(f"{self.name} 开始规划任务...")
        todo = self.rc.todo

        if isinstance(todo, UserRequirement):
            # 1. 规划任务
            plan = await self.actions[0].run(todo.content)

            # 2. 保存任务计划
            self.write_report("../task_plan.md", plan)

            # 3. 生成分析报告
            messages = self._message_bus.get_history()
            if messages:
                report = MarkdownFormatter.generate_report(messages)
                self.write_report("../code_analysis_report.md", report)

            return Message(content=plan, role=self.profile, cause_by=type(todo))

        return Message(content="无效的任务类型", role=self.profile, cause_by=type(todo))

    def write_report(self, filename: str, content: str):
        """写入报告文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"报告已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存报告失败: {str(e)}")


class FileParser(BaseAnalyzerRole):
    """文件解析者角色"""
    name: str = "FileParser"
    profile: str = "文件路径解析专家"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.set_actions([ParseFilePath])

    async def _act(self) -> Message:
        logger.info(f"{self.name} 开始解析文件...")
        todo = self.rc.todo

        if isinstance(todo, Message):
            target_path = todo.content
            result = await self.actions[0].run(target_path)

            # 发布消息
            self.publish_message(result)

            # 格式化结果
            content = MarkdownFormatter.format_file_analysis(result)
            return Message(content=content, role=self.profile, cause_by=type(todo))

        return Message(content="无效的任务类型", role=self.profile, cause_by=type(todo))


class CodeStructureAnalyzer(BaseAnalyzerRole):
    """代码框架分析者角色"""
    name: str = "CodeStructureAnalyzer"
    profile: str = "代码结构分析专家"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.set_actions([AnalyzeCodeStructure])

    def subscribe_messages(self):
        """订阅消息"""
        self._message_bus.subscribe("FILE_ANALYSIS", self.handle_file_analysis)

    def handle_file_analysis(self, message: AgentMessage):
        """处理文件分析消息"""
        logger.info(f"收到文件分析结果，准备分析代码结构: {message}")
        # 可以在这里触发代码结构分析

    async def _act(self) -> Message:
        logger.info(f"{self.name} 开始分析代码结构...")
        todo = self.rc.todo

        if isinstance(todo, Message):
            file_path = todo.content
            result = await self.actions[0].run(file_path)

            # 发布消息
            self.publish_message(result)

            # 格式化结果
            content = MarkdownFormatter.format_code_structure(result)
            return Message(content=content, role=self.profile, cause_by=type(todo))

        return Message(content="无效的任务类型", role=self.profile, cause_by=type(todo))


class CodeFunctionAnalyzer(BaseAnalyzerRole):
    """类函数方法分析者角色"""
    name: str = "CodeFunctionAnalyzer"
    profile: str = "代码函数分析专家"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.set_actions([AnalyzeCodeFunction])

    def subscribe_messages(self):
        """订阅消息"""
        self._message_bus.subscribe("CODE_STRUCTURE", self.handle_code_structure)

    def handle_code_structure(self, message: AgentMessage):
        """处理代码结构分析消息"""
        logger.info(f"收到代码结构分析结果，准备分析函数: {message}")
        # 可以在这里触发函数分析

    async def _act(self) -> Message:
        logger.info(f"{self.name} 开始分析代码函数...")
        todo = self.rc.todo

        if isinstance(todo, Message) and isinstance(todo.content, dict):
            result = await self.actions[0].run(
                todo.content['file_path'],
                todo.content['start_line'],
                todo.content['end_line'],
                todo.content['block_type'],
                todo.content['block_name']
            )

            # 发布消息
            self.publish_message(result)

            # 格式化结果
            content = MarkdownFormatter.format_code_function(result)
            return Message(content=content, role=self.profile, cause_by=type(todo))

        return Message(content="无效的任务类型", role=self.profile, cause_by=type(todo))


# ======= 主函数 =======

async def main(
        target_path: str = ".",
        investment: float = 3.0,
        output_file: str = "code_analysis_report.md"
):
    """
    主函数
    
    Args:
        target_path: 目标路径
        investment: 投资额度
        output_file: 输出文件名
    """
    logger.info(f"开始分析目标: {target_path}")

    # 创建分析团队
    task_planner = TaskPlanner()
    file_parser = FileParser()
    code_structure_analyzer = CodeStructureAnalyzer()
    code_function_analyzer = CodeFunctionAnalyzer()

    # 注册消息处理
    file_parser.subscribe_messages()
    code_structure_analyzer.subscribe_messages()
    code_function_analyzer.subscribe_messages()
    task_planner.subscribe_messages()

    # 1. 解析文件路径
    logger.info("正在解析文件路径...")
    parse_action = ParseFilePath()
    file_analysis_msg = await parse_action.run(target_path)
    file_analysis_msg.sender = file_parser.name
    MessageBus().publish(file_analysis_msg)

    # 2. 分析代码结构
    logger.info("正在分析代码结构...")

    # 获取要分析的文件列表
    files_to_analyze = []
    if file_analysis_msg.content['is_file']:
        # 如果是单个文件
        files_to_analyze = [file_analysis_msg.content['files'][0]]
    else:
        # 如果是目录，分析目录下的所有文件
        files_to_analyze = file_analysis_msg.content['files']

    # 对每个文件进行分析
    for file_path in files_to_analyze:
        # 只分析Python文件
        if not file_path.endswith('.py'):
            logger.info(f"跳过非Python文件: {file_path}")
            continue

        logger.info(f"正在分析文件: {file_path}")
        analyze_structure_action = AnalyzeCodeStructure()
        code_structure_msg = await analyze_structure_action.run(file_path)
        code_structure_msg.sender = code_structure_analyzer.name
        MessageBus().publish(code_structure_msg)

        # 3. 分析代码函数
        logger.info(f"正在分析文件 {file_path} 中的代码...")

        # 分析独立代码块
        if code_structure_msg.content['standalone_code']:
            logger.info("正在分析独立代码块...")
            # 将相邻的独立代码块组合在一起进行分析
            blocks = code_structure_msg.content['standalone_code']
            i = 0
            while i < len(blocks):
                start_block = blocks[i]
                end_block = start_block

                # 查找相邻的代码块
                while (i + 1 < len(blocks) and
                       blocks[i + 1]['start_line'] <= end_block['end_line'] + 2):
                    end_block = blocks[i + 1]
                    i += 1

                # 分析这组代码块
                analyze_function_action = AnalyzeCodeFunction()
                block_name = f"独立代码块 ({start_block['type']})"
                function_msg = await analyze_function_action.run(
                    file_path,
                    start_block['start_line'],
                    end_block['end_line'],
                    'standalone',
                    block_name
                )
                function_msg.sender = code_function_analyzer.name
                MessageBus().publish(function_msg)
                i += 1

        # 分析独立函数
        for func in code_structure_msg.content['functions']:
            logger.info(f"正在分析函数: {func['name']}...")
            analyze_function_action = AnalyzeCodeFunction()
            function_msg = await analyze_function_action.run(
                file_path,
                func['start_line'],
                func['end_line'],
                'function',
                func['name']
            )
            function_msg.sender = code_function_analyzer.name
            MessageBus().publish(function_msg)

        # 分析类方法
        for cls in code_structure_msg.content['classes']:
            class_name = cls['name']
            for method in cls['methods']:
                method_name = f"{class_name}.{method['name']}"
                logger.info(f"正在分析方法: {method_name}...")
                analyze_function_action = AnalyzeCodeFunction()
                function_msg = await analyze_function_action.run(
                    file_path,
                    method['start_line'],
                    method['end_line'],
                    'method',
                    method_name
                )
                function_msg.sender = code_function_analyzer.name
                MessageBus().publish(function_msg)

    # 4. 生成报告
    logger.info("正在生成报告...")
    messages = MessageBus().get_history()
    report_content = MarkdownFormatter.generate_report(messages)

    # 保存报告
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(report_content)

    logger.info(f"分析完成，结果已保存到: {output_file}")


if __name__ == "__main__":
    import asyncio
    import fire

    fire.Fire(main)
